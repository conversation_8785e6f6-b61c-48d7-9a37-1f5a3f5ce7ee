<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\PermissionService;
use Illuminate\Http\Request; 
use Illuminate\Support\Facades\Log;

/**
 * 权限管理控制器
 * 处理用户权限、角色权限、API权限等
 */
class PermissionController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * @ApiTitle(获取用户权限)
     * @ApiSummary(查询用户的权限列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/permissions/user)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=false, description="用户ID，不传则查询当前用户")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "user_id": 123,
     *     "username": "testuser",
     *     "role": "premium_user",
     *     "permissions": [
     *       {
     *         "id": 1,
     *         "name": "ai.image.generate",
     *         "display_name": "AI图像生成",
     *         "category": "ai_generation",
     *         "granted": true,
     *         "source": "role",
     *         "expires_at": null
     *       },
     *       {
     *         "id": 2,
     *         "name": "ai.video.generate",
     *         "display_name": "AI视频生成",
     *         "category": "ai_generation",
     *         "granted": false,
     *         "source": null,
     *         "expires_at": null
     *       }
     *     ],
     *     "quotas": {
     *       "daily_image_generation": 50,
     *       "daily_video_generation": 5,
     *       "max_file_size": 104857600
     *     },
     *     "restrictions": {
     *       "can_batch_generate": true,
     *       "can_use_premium_models": true,
     *       "max_concurrent_tasks": 3
     *     }
     *   }
     * })
     */
    public function getUserPermissions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $targetUserId = $request->get('user_id');

            // 检查权限：管理员可查看所有用户权限，普通用户只能查看自己的权限
            if ($targetUserId && $targetUserId != $user->id && !$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，只能查看自己的权限信息');
            }

            $rules = [
                'user_id' => 'sometimes|integer|exists:users,id'
            ];

            $this->validateData($request->all(), $rules);

            $userId = $targetUserId ?: $user->id;

            $result = $this->permissionService->getUserPermissions($userId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('用户权限详情获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '用户权限详情获取失败']);
        }
    }

    /**
     * @ApiTitle(检查用户权限)
     * @ApiSummary(检查用户是否具有指定权限)
     * @ApiMethod(POST)
     * @ApiRoute(/api/permissions/check)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="permission", type="string", required=true, description="权限名称")
     * @ApiParams(name="user_id", type="integer", required=false, description="用户ID，不传则检查当前用户")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "user_id": 123,
     *     "permission": "ai.image.generate",
     *     "granted": true,
     *     "source": "role",
     *     "expires_at": null,
     *     "remaining_quota": 45,
     *     "restrictions": {
     *       "max_concurrent_tasks": 3,
     *       "current_tasks": 1
     *     }
     *   }
     * })
     */
    public function checkPermission(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $targetUserId = $request->get('user_id');

            // 检查权限：管理员可检查所有用户权限，普通用户只能检查自己的权限
            if ($targetUserId && $targetUserId != $user->id && !$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，只能检查自己的权限');
            }

            $rules = [
                'permission' => 'required|string|max:100',
                'user_id' => 'sometimes|integer|exists:users,id'
            ];

            $messages = [
                'permission.required' => '权限名称不能为空',
                'permission.max' => '权限名称不能超过100个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $userId = $targetUserId ?: $user->id;
            $permission = $request->permission;

            $result = $this->permissionService->checkPermission($userId, $permission);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('权限检查失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '权限检查失败']);
        }
    }

    /**
     * @ApiTitle(获取角色列表)
     * @ApiSummary(查询系统角色列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/permissions/roles)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "roles": [
     *       {
     *         "id": 1,
     *         "name": "free_user",
     *         "display_name": "免费用户",
     *         "description": "基础功能使用权限",
     *         "level": 1,
     *         "permissions_count": 15,
     *         "users_count": 1250,
     *         "quotas": {
     *           "daily_image_generation": 10,
     *           "daily_video_generation": 0,
     *           "max_file_size": 10485760
     *         },
     *         "created_at": "2024-01-01 12:00:00"
     *       },
     *       {
     *         "id": 2,
     *         "name": "premium_user",
     *         "display_name": "高级用户",
     *         "description": "高级功能使用权限",
     *         "level": 2,
     *         "permissions_count": 35,
     *         "users_count": 450,
     *         "quotas": {
     *           "daily_image_generation": 50,
     *           "daily_video_generation": 5,
     *           "max_file_size": 104857600
     *         },
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ]
     *   }
     * })
     */
    public function getRoles(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看角色列表');
            }

            $result = $this->permissionService->getRoles();

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('角色列表获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '角色列表获取失败']);
        }
    }

    /**
     * @ApiTitle(分配用户角色)
     * @ApiSummary(为用户分配新的角色)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/permissions/assign-role)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams(name="role_name", type="string", required=true, description="角色名称")
     * @ApiParams(name="expires_at", type="string", required=false, description="过期时间")
     * @ApiParams(name="reason", type="string", required=false, description="分配原因")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "角色分配成功",
     *   "data": {
     *     "user_id": 123,
     *     "old_role": "free_user",
     *     "new_role": "premium_user",
     *     "expires_at": "2024-12-31 23:59:59",
     *     "assigned_by": "admin",
     *     "assigned_at": "2024-01-01 12:00:00",
     *     "reason": "用户升级为高级会员"
     *   }
     * })
     */
    public function assignRole(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可分配角色');
            }

            $rules = [
                'user_id' => 'required|integer|exists:users,id',
                'role_name' => 'required|string|exists:roles,name',
                'expires_at' => 'sometimes|date_format:Y-m-d H:i:s|after:now',
                'reason' => 'sometimes|string|max:500'
            ];

            $messages = [
                'user_id.required' => '用户ID不能为空',
                'user_id.exists' => '用户不存在',
                'role_name.required' => '角色名称不能为空',
                'role_name.exists' => '角色不存在',
                'expires_at.after' => '过期时间必须晚于当前时间'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $assignData = [
                'user_id' => $request->user_id,
                'role_name' => $request->role_name,
                'expires_at' => $request->get('expires_at'),
                'reason' => $request->get('reason', ''),
                'assigned_by' => $user->id
            ];

            $result = $this->permissionService->assignRole($assignData['user_id'], $assignData['role']);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('角色分配失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '角色分配失败']);
        }
    }

    /**
     * @ApiTitle(授予用户权限)
     * @ApiSummary(为用户授予特定权限)
     * @ApiMethod(POST)
     * @ApiRoute(/api/permissions/grant)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams(name="permissions", type="array", required=true, description="权限名称数组")
     * @ApiParams(name="expires_at", type="string", required=false, description="过期时间")
     * @ApiParams(name="reason", type="string", required=false, description="授权原因")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "权限授予成功",
     *   "data": {
     *     "user_id": 123,
     *     "granted_permissions": [
     *       {
     *         "permission": "ai.video.generate",
     *         "expires_at": "2024-12-31 23:59:59"
     *       }
     *     ],
     *     "failed_permissions": [],
     *     "granted_by": "admin",
     *     "granted_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function grantPermissions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可授予权限');
            }

            $rules = [
                'user_id' => 'required|integer|exists:users,id',
                'permissions' => 'required|array|min:1|max:20',
                'permissions.*' => 'required|string|exists:permissions,name',
                'expires_at' => 'sometimes|date_format:Y-m-d H:i:s|after:now',
                'reason' => 'sometimes|string|max:500'
            ];

            $messages = [
                'user_id.required' => '用户ID不能为空',
                'user_id.exists' => '用户不存在',
                'permissions.required' => '权限列表不能为空',
                'permissions.array' => '权限列表必须是数组格式',
                'permissions.min' => '至少需要1个权限',
                'permissions.max' => '最多支持20个权限',
                'permissions.*.exists' => '权限不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $grantData = [
                'user_id' => $request->user_id,
                'permissions' => $request->permissions,
                'expires_at' => $request->get('expires_at'),
                'reason' => $request->get('reason', ''),
                'granted_by' => $user->id
            ];

            $result = $this->permissionService->grantPermissions($grantData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('权限授予失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '权限授予失败']);
        }
    }

    /**
     * @ApiTitle(撤销用户权限)
     * @ApiSummary(撤销用户的特定权限)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/permissions/revoke)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams(name="permissions", type="array", required=true, description="权限名称数组")
     * @ApiParams(name="reason", type="string", required=false, description="撤销原因")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "权限撤销成功",
     *   "data": {
     *     "user_id": 123,
     *     "revoked_permissions": [
     *       "ai.video.generate"
     *     ],
     *     "failed_permissions": [],
     *     "revoked_by": "admin",
     *     "revoked_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function revokePermissions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可撤销权限');
            }

            $rules = [
                'user_id' => 'required|integer|exists:users,id',
                'permissions' => 'required|array|min:1|max:20',
                'permissions.*' => 'required|string',
                'reason' => 'sometimes|string|max:500'
            ];

            $messages = [
                'user_id.required' => '用户ID不能为空',
                'user_id.exists' => '用户不存在',
                'permissions.required' => '权限列表不能为空',
                'permissions.array' => '权限列表必须是数组格式',
                'permissions.min' => '至少需要1个权限',
                'permissions.max' => '最多支持20个权限'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $revokeData = [
                'user_id' => $request->user_id,
                'permissions' => $request->permissions,
                'reason' => $request->get('reason', ''),
                'revoked_by' => $user->id
            ];

            $result = $this->permissionService->revokePermissions($revokeData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('权限撤销失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['code' => ApiCodeEnum::CONTROLLER_ERROR, 'message' => '权限撤销失败']);
        }
    }

    /**
     * @ApiTitle(获取权限历史)
     * @ApiSummary(查询用户权限变更历史)
     * @ApiMethod(GET)
     * @ApiRoute(/api/permissions/history)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=true, description="用户ID")
     * @ApiParams(name="action", type="string", required=false, description="操作类型：grant,revoke,assign_role")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "user_id": 123,
     *     "history": [
     *       {
     *         "id": 1,
     *         "action": "grant",
     *         "permission": "ai.video.generate",
     *         "old_value": null,
     *         "new_value": "granted",
     *         "expires_at": "2024-12-31 23:59:59",
     *         "operator": "admin",
     *         "operator_id": 1,
     *         "reason": "用户升级为高级会员",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 15,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function getPermissionHistory(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看权限历史');
            }

            $rules = [
                'user_id' => 'required|integer|exists:users,id',
                'action' => 'sometimes|string|in:grant,revoke,assign_role',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'user_id' => $request->user_id,
                'action' => $request->get('action'),
                'page' => $request->get('page', 1),
                'per_page' => 20
            ];

            $result = $this->permissionService->getPermissionHistory($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取权限历史失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取权限历史失败');
        }
    }
}
