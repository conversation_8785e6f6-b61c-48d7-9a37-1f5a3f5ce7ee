<?php

namespace App\Helpers;

class LogCheckHelper
{
    /** @var array 定义需要脱敏的敏感字段键名 */
    protected const SENSITIVE_KEYS = [
        'password', 'password_confirmation', 'token', 'refresh_token',
        'api_key', 'secret', 'credit_card_number', 'cvv'
    ];

    /** @var int 定义字符串截断的最大长度 */
    protected const TRUNCATION_LENGTH = 10000;

    /** @var int 定义递归处理的最大深度，防止无限循环或栈溢出，投产后改为10 */
    protected const MAX_RECURSION_DEPTH = 100;

    /**
     * 清理和脱敏请求数据以用于日志记录（防日志爆炸版）。
     *
     * @param array $data        要处理的数据
     * @param int   $currentDepth 当前递归深度
     * @return array|string      处理后的数据
     */
    protected function sanitize_request_for_log(array $data, int $currentDepth = 0)
    {
        // 策略三：深度限制
        if ($currentDepth >= self::MAX_RECURSION_DEPTH) {
            return '[Max Depth Reached]';
        }

        foreach ($data as $key => &$value) {
            // 检查键名是否敏感
            if (in_array($key, self::SENSITIVE_KEYS, true)) {
                $value = '********';
                continue;
            }

            // 策略二：过滤特定对象类型 (最常见的文件上传)
            if (is_object($value) && $value instanceof \Illuminate\Http\UploadedFile) {
                // 不记录文件内容，只记录文件摘要信息
                $value = sprintf(
                    '[Uploaded File: %s (%s KB)]',
                    $value->getClientOriginalName(),
                    round($value->getSize() / 1024, 2)
                );
                continue;
            }

            // 策略二：过滤特定对象类型 (最常见的文件上传)
            if (is_object($value) && $value instanceof \Illuminate\Http\UploadedFile) {
                // --- 这是修改后的核心逻辑 ---
                // 不再返回一个简单的字符串，而是返回一个包含详细文件信息的结构化数组。
                $value = [
                    '_log_type'       => 'Uploaded File Info', // 一个标记，表明这是文件信息
                    'original_name'   => $value->getClientOriginalName(),
                    'mime_type'       => $value->getClientMimeType(),
                    'size_kb'         => round($value->getSize() / 1024, 2),
                    'is_valid'        => $value->isValid(),
                    'error'           => $value->getError(), // 0 表示 UPLOAD_ERR_OK (无错误)
                    'error_message'   => $value->getErrorMessage(),
                ];
                continue;
            }


            // 递归处理数组
            if (is_array($value)) {
                $value = $this->sanitize_request_for_log($value, $currentDepth + 1);
            }
            
            // 策略一：截断过长的字符串
            elseif (is_string($value)) {
                // 过滤 Base64 编码的字符串（通常是图片上传）
                if (str_starts_with($value, 'data:image')) {
                     $value = '[Base64 Image Data]';
                }
                // 截断普通的长字符串
                elseif (mb_strlen($value) > self::TRUNCATION_LENGTH) {
                    $value = mb_substr($value, 0, self::TRUNCATION_LENGTH) . '... [TRUNCATED]';
                }
            }
        }

        return $data;
    }
}
