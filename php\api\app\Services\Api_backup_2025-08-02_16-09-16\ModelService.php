<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * AI模型服务类
 * 负责AI模型的管理、调用、监控和配置
 */
class ModelService extends Service
{
    /**
     * 获取可用的AI模型列表
     */
    public function getAvailableModels(array $filters = []): array
    {
        try {
            $query = DB::table('ai_models')->where('status', 'active');
            
            // 应用过滤器
            if (!empty($filters['type'])) {
                $query->where('type', $filters['type']);
            }
            
            if (!empty($filters['provider'])) {
                $query->where('provider', $filters['provider']);
            }
            
            if (!empty($filters['capability'])) {
                $query->whereJsonContains('capabilities', $filters['capability']);
            }
            
            $models = $query->orderBy('priority', 'desc')
                ->orderBy('name', 'asc')
                ->get()
                ->map(function($model) {
                    $model->capabilities = json_decode($model->capabilities, true) ?? [];
                    $model->config = json_decode($model->config, true) ?? [];
                    $model->pricing = json_decode($model->pricing, true) ?? [];
                    return $model;
                });
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型列表获取成功',
                'data' => [
                    'models' => $models->toArray(),
                    'total_count' => $models->count(),
                    'filters_applied' => $filters
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'filters' => $filters,
            ];

            Log::error('获取AI模型列表失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取AI模型列表失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取模型详细信息
     */
    public function getModelDetails(string $modelId): array
    {
        try {
            $model = DB::table('ai_models')->where('id', $modelId)->first();
            
            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模型不存在',
                    'data' => null
                ];
            }
            
            // 解析JSON字段
            $model->capabilities = json_decode($model->capabilities, true) ?? [];
            $model->config = json_decode($model->config, true) ?? [];
            $model->pricing = json_decode($model->pricing, true) ?? [];
            $model->limits = json_decode($model->limits, true) ?? [];
            
            // 获取模型使用统计
            $stats = $this->getModelUsageStats($modelId);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型详情获取成功',
                'data' => [
                    'model' => $model,
                    'usage_stats' => $stats['data'] ?? []
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'model_id' => $modelId,
            ];

            Log::error('获取模型详情失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取模型详情失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 调用AI模型
     */
    public function invokeModel(string $modelId, array $requestData, int $userId): array
    {
        try {
            // 获取模型信息
            $model = DB::table('ai_models')
                ->where('id', $modelId)
                ->where('status', 'active')
                ->first();
            
            if (!$model) {
                return [
                    'code' => ApiCodeEnum::MODEL_NOT_FOUND,
                    'message' => '模型不存在或不可用',
                    'data' => null
                ];
            }
            
            // 验证请求数据
            $validationResult = $this->validateModelRequest($model, $requestData);
            if ($validationResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $validationResult;
            }
            
            // 检查用户权限和配额
            $permissionResult = $this->checkUserPermission($userId, $modelId);
            if ($permissionResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $permissionResult;
            }
            
            // 创建调用记录
            $invocationId = $this->createInvocationRecord($modelId, $userId, $requestData);
            
            // 调用模型API
            $startTime = microtime(true);
            $apiResult = $this->callModelAPI($model, $requestData);
            $endTime = microtime(true);
            
            $responseTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
            
            // 更新调用记录
            $this->updateInvocationRecord($invocationId, $apiResult, $responseTime);
            
            // 更新用户配额
            $this->updateUserQuota($userId, $modelId, $apiResult);
            
            Log::info('AI模型调用成功', [
                'model_id' => $modelId,
                'user_id' => $userId,
                'invocation_id' => $invocationId,
                'response_time' => $responseTime
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型调用成功',
                'data' => [
                    'invocation_id' => $invocationId,
                    'model_id' => $modelId,
                    'response' => $apiResult['response'] ?? null,
                    'response_time' => $responseTime,
                    'tokens_used' => $apiResult['tokens_used'] ?? 0,
                    'cost' => $apiResult['cost'] ?? 0
                ]
            ];
        } catch (\Exception $e) {
            Log::error('AI模型调用失败', [
                'model_id' => $modelId,
                'user_id' => $userId,
                'request_data' => $requestData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI模型调用失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取模型使用统计
     */
    public function getModelUsageStats(string $modelId, string $period = 'day'): array
    {
        try {
            $startDate = match($period) {
                'hour' => Carbon::now()->subHour(),
                'day' => Carbon::now()->subDay(),
                'week' => Carbon::now()->subWeek(),
                'month' => Carbon::now()->subMonth(),
                default => Carbon::now()->subDay()
            };
            
            $stats = DB::table('model_invocations')
                ->where('model_id', $modelId)
                ->where('created_at', '>=', $startDate)
                ->selectRaw('
                    COUNT(*) as total_invocations,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_invocations,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_invocations,
                    AVG(response_time_ms) as avg_response_time,
                    SUM(tokens_used) as total_tokens,
                    SUM(cost) as total_cost,
                    COUNT(DISTINCT user_id) as unique_users
                ')
                ->first();
            
            $successRate = $stats->total_invocations > 0 
                ? round(($stats->successful_invocations / $stats->total_invocations) * 100, 2)
                : 0;
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型使用统计获取成功',
                'data' => [
                    'period' => $period,
                    'start_date' => $startDate->format('Y-m-d H:i:s'),
                    'total_invocations' => (int)$stats->total_invocations,
                    'successful_invocations' => (int)$stats->successful_invocations,
                    'failed_invocations' => (int)$stats->failed_invocations,
                    'success_rate' => $successRate,
                    'avg_response_time' => round($stats->avg_response_time ?? 0, 2),
                    'total_tokens' => (int)$stats->total_tokens,
                    'total_cost' => round($stats->total_cost ?? 0, 4),
                    'unique_users' => (int)$stats->unique_users
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取模型使用统计失败', [
                'model_id' => $modelId,
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模型使用统计获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 更新模型配置
     */
    public function updateModelConfig(string $modelId, array $config): array
    {
        try {
            $model = DB::table('ai_models')->where('id', $modelId)->first();
            
            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模型不存在',
                    'data' => null
                ];
            }
            
            // 验证配置数据
            $validationResult = $this->validateModelConfig($config);
            if ($validationResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $validationResult;
            }
            
            // 合并现有配置
            $currentConfig = json_decode($model->config, true) ?? [];
            $newConfig = array_merge($currentConfig, $config);
            
            // 更新模型配置
            DB::table('ai_models')
                ->where('id', $modelId)
                ->update([
                    'config' => json_encode($newConfig),
                    'updated_at' => Carbon::now()
                ]);
            
            Log::info('模型配置更新成功', [
                'model_id' => $modelId,
                'config_changes' => $config
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型配置更新成功',
                'data' => [
                    'model_id' => $modelId,
                    'updated_config' => $newConfig,
                    'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];
        } catch (\Exception $e) {
            Log::error('模型配置更新失败', [
                'model_id' => $modelId,
                'config' => $config,
                'error' => $e->getMessage()
            ]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模型配置更新失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取用户模型使用情况
     */
    public function getUserModelUsage(int $userId, string $period = 'month'): array
    {
        try {
            $startDate = match($period) {
                'day' => Carbon::now()->subDay(),
                'week' => Carbon::now()->subWeek(),
                'month' => Carbon::now()->subMonth(),
                'year' => Carbon::now()->subYear(),
                default => Carbon::now()->subMonth()
            };
            
            $usage = DB::table('model_invocations')
                ->where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->selectRaw('
                    model_id,
                    COUNT(*) as invocation_count,
                    SUM(tokens_used) as total_tokens,
                    SUM(cost) as total_cost,
                    AVG(response_time_ms) as avg_response_time,
                    MAX(created_at) as last_used_at
                ')
                ->groupBy('model_id')
                ->orderBy('invocation_count', 'desc')
                ->get();
            
            // 获取模型名称
            $modelIds = $usage->pluck('model_id')->toArray();
            $models = DB::table('ai_models')
                ->whereIn('id', $modelIds)
                ->pluck('name', 'id');
            
            $usageData = $usage->map(function($item) use ($models) {
                return [
                    'model_id' => $item->model_id,
                    'model_name' => $models[$item->model_id] ?? 'Unknown',
                    'invocation_count' => (int)$item->invocation_count,
                    'total_tokens' => (int)$item->total_tokens,
                    'total_cost' => round($item->total_cost, 4),
                    'avg_response_time' => round($item->avg_response_time, 2),
                    'last_used_at' => $item->last_used_at
                ];
            });
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '用户模型使用情况获取成功',
                'data' => [
                    'user_id' => $userId,
                    'period' => $period,
                    'start_date' => $startDate->format('Y-m-d H:i:s'),
                    'usage' => $usageData->toArray(),
                    'summary' => [
                        'total_invocations' => $usageData->sum('invocation_count'),
                        'total_tokens' => $usageData->sum('total_tokens'),
                        'total_cost' => round($usageData->sum('total_cost'), 4),
                        'models_used' => $usageData->count()
                    ]
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取用户模型使用情况失败', [
                'user_id' => $userId,
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户模型使用情况获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 验证模型请求数据
     */
    private function validateModelRequest($model, array $requestData): array
    {
        $config = json_decode($model->config, true) ?? [];
        $limits = json_decode($model->limits, true) ?? [];
        
        // 检查必需参数
        $requiredParams = $config['required_params'] ?? [];
        foreach ($requiredParams as $param) {
            if (!isset($requestData[$param])) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => "缺少必需参数：{$param}",
                    'data' => null
                ];
            }
        }
        
        // 检查输入长度限制
        if (isset($limits['max_input_length']) && isset($requestData['input'])) {
            if (strlen($requestData['input']) > $limits['max_input_length']) {
                return [
                    'code' => ApiCodeEnum::INPUT_TOO_LONG,
                    'message' => '输入内容超过长度限制',
                    'data' => ['max_length' => $limits['max_input_length']]
                ];
            }
        }
        
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '验证通过',
            'data' => null
        ];
    }
    
    /**
     * 检查用户权限
     */
    private function checkUserPermission(int $userId, string $modelId): array
    {
        // 检查用户是否有使用该模型的权限
        $hasPermission = DB::table('user_model_permissions')
            ->where('user_id', $userId)
            ->where('model_id', $modelId)
            ->where('status', 'active')
            ->exists();
        
        if (!$hasPermission) {
            return [
                'code' => ApiCodeEnum::PERMISSION_DENIED,
                'message' => '用户无权使用该模型',
                'data' => null
            ];
        }
        
        // 检查配额限制
        $quota = DB::table('user_model_quotas')
            ->where('user_id', $userId)
            ->where('model_id', $modelId)
            ->first();
        
        if ($quota && $quota->used_count >= $quota->limit_count) {
            return [
                'code' => ApiCodeEnum::QUOTA_EXCEEDED,
                'message' => '用户配额已用完',
                'data' => [
                    'used' => $quota->used_count,
                    'limit' => $quota->limit_count
                ]
            ];
        }
        
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '权限检查通过',
            'data' => null
        ];
    }
    
    /**
     * 调用模型API
     */
    private function callModelAPI($model, array $requestData): array
    {
        $config = json_decode($model->config, true) ?? [];
        
        // 模拟API调用（实际实现中应该调用真实的AI模型API）
        $response = [
            'success' => true,
            'response' => [
                'text' => '这是模拟的AI模型响应',
                'confidence' => 0.95,
                'model_version' => $model->version ?? '1.0'
            ],
            'tokens_used' => rand(50, 200),
            'cost' => rand(1, 10) / 100
        ];
        
        return $response;
    }
    
    /**
     * 创建调用记录
     */
    private function createInvocationRecord(string $modelId, int $userId, array $requestData): string
    {
        $invocationId = 'inv_' . uniqid();
        
        DB::table('model_invocations')->insert([
            'id' => $invocationId,
            'model_id' => $modelId,
            'user_id' => $userId,
            'request_data' => json_encode($requestData),
            'status' => 'processing',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
        
        return $invocationId;
    }
    
    /**
     * 更新调用记录
     */
    private function updateInvocationRecord(string $invocationId, array $apiResult, float $responseTime): void
    {
        DB::table('model_invocations')
            ->where('id', $invocationId)
            ->update([
                'status' => $apiResult['success'] ? 'success' : 'failed',
                'response_data' => json_encode($apiResult['response'] ?? null),
                'tokens_used' => $apiResult['tokens_used'] ?? 0,
                'cost' => $apiResult['cost'] ?? 0,
                'response_time_ms' => $responseTime,
                'error_message' => $apiResult['error'] ?? null,
                'updated_at' => Carbon::now()
            ]);
    }
    
    /**
     * 更新用户配额
     */
    private function updateUserQuota(int $userId, string $modelId, array $apiResult): void
    {
        if ($apiResult['success']) {
            DB::table('user_model_quotas')
                ->where('user_id', $userId)
                ->where('model_id', $modelId)
                ->increment('used_count');
        }
    }
    
    /**
     * 验证模型配置
     */
    private function validateModelConfig(array $config): array
    {
        // 基本配置验证
        $allowedKeys = [
            'api_endpoint', 'api_key', 'timeout', 'max_retries',
            'required_params', 'optional_params', 'response_format'
        ];
        
        foreach ($config as $key => $value) {
            if (!in_array($key, $allowedKeys)) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => "无效的配置项：{$key}",
                    'data' => ['allowed_keys' => $allowedKeys]
                ];
            }
        }
        
        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '配置验证通过',
            'data' => null
        ];
    }
}