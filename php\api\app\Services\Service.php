<?php

namespace App\Services;

class Service
{
    /**
     * 定义需要脱敏的敏感字段键名。
     * 使用 const 可以提高性能，并方便在多处复用。
     */
    protected const SENSITIVE_KEYS = [
        'password',
        'password_confirmation',
        'token',
        'refresh_token',
        'api_key',
        'secret',
        'credit_card_number',
        'cvv'
    ];

    /**
     * 清理和脱敏请求数据以用于日志记录（支持多维数组）。
     *
     * @param array $data 要处理的数据
     * @return array 处理后的数据
     */
    protected function sanitize_request_for_log(array $data): array
    {
        // 遍历数组，注意这里使用了引用（&），可以直接修改原数组的值
        foreach ($data as $key => &$value) {
            
            // 1. 首先检查键名是否是敏感的
            if (in_array($key, self::SENSITIVE_KEYS, true)) {
                $value = '********';
                continue; // 替换后，直接进行下一次循环
            }

            // 2. 如果值本身是一个数组，则进行递归调用
            if (is_array($value)) {
                $value = $this->sanitize_request_for_log($value);
            }
            
            // 3. 如果值是一个过长的字符串，则进行截断
            //    (注意：这个检查在 is_array 之后，确保我们不会尝试截断一个数组)
            elseif (is_string($value) && mb_strlen($value) > 1024) {
                $value = mb_substr($value, 0, 1024) . '... [TRUNCATED]';
            }
        }

        return $data;
    }
}
