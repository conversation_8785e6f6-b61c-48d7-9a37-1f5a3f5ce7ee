<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\ModelManagementService;
use App\Services\Api\AiPlatformSelectionService;
use App\Services\Api\AiPlatformHealthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * AI模型管理与健康检查
 */
class AiModelController extends Controller
{
    protected $modelService;
    protected $platformSelectionService;
    protected $platformHealthService;

    public function __construct(
        ModelManagementService $modelService,
        AiPlatformSelectionService $platformSelectionService,
        AiPlatformHealthService $platformHealthService
    ) {
        // 🔧 修正：添加中间件配置
        $this->middleware('auth:api');
        $this->middleware('throttle:60,1');

        $this->modelService = $modelService;
        $this->platformSelectionService = $platformSelectionService;
        $this->platformHealthService = $platformHealthService;
    }

    /**
     * @ApiTitle(获取可用模型)
     * @ApiSummary(获取用户可用的AI模型列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/available)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="模型类型：story/image/voice/video/music/sound")
     * @ApiParams(name="category", type="string", required=false, description="模型分类：free/premium/custom")
     * @ApiParams(name="sort", type="string", required=false, description="排序方式：popular/latest/rating")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "models": [
     *       {
     *         "model_id": "deepseek-story",
     *         "name": "DeepSeek 故事生成",
     *         "description": "强大的故事创作AI模型",
     *         "type": "story",
     *         "category": "premium",
     *         "provider": "DeepSeek",
     *         "version": "2.0",
     *         "capabilities": ["长篇故事", "多角色对话", "情节构建"],
     *         "pricing": {
     *           "type": "token_based",
     *           "cost_per_1k_tokens": 0.03,
     *           "free_quota": 1000
     *         },
     *         "performance": {
     *           "quality_score": 9.5,
     *           "speed_score": 8.0,
     *           "creativity_score": 9.2
     *         },
     *         "usage_stats": {
     *           "total_uses": 15420,
     *           "user_rating": 4.8,
     *           "success_rate": 96.5
     *         },
     *         "is_available": true,
     *         "requires_subscription": true,
     *         "estimated_response_time": "5-15秒"
     *       }
     *     ],
     *     "categories": [
     *       {"name": "free", "count": 8, "description": "免费模型"},
     *       {"name": "premium", "count": 15, "description": "高级模型"},
     *       {"name": "custom", "count": 3, "description": "自定义模型"}
     *     ],
     *     "user_quotas": {
     *       "free_tokens_remaining": 2500,
     *       "premium_tokens_remaining": 10000,
     *       "daily_limit": 50000,
     *       "monthly_limit": 1000000
     *     }
     *   }
     * })
     */
    public function available(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:story,image,voice,video,music,sound',
                'category' => 'sometimes|string|in:free,premium,custom',
                'sort' => 'sometimes|string|in:popular,latest,rating,name'
            ];

            try {
                $this->validateData($request->all(), $rules);
            } catch (\Illuminate\Validation\ValidationException $e) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
            }

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $filters = [
                'type' => $request->get('type'),
                'category' => $request->get('category'),
                'sort' => $request->get('sort', 'popular')
            ];

            $result = $this->modelService->getAvailableModels($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取模型列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取模型列表失败', []);
        }
    }

    /**
     * @ApiTitle(获取模型详情)
     * @ApiSummary(获取指定AI模型的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/{model_id}/detail)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="model_id", type="string", required=true, description="模型ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "model_id": "deepseek-story",
     *     "name": "DeepSeek 故事生成",
     *     "description": "强大的故事创作AI模型，支持多种文学体裁和写作风格",
     *     "type": "story",
     *     "category": "premium",
     *     "provider": "DeepSeek",
     *     "version": "4.0",
     *     "capabilities": ["长篇故事创作", "多角色对话", "情节构建"],
     *     "pricing": {
     *       "type": "token_based",
     *       "cost_per_1k_tokens": 0.03,
     *       "free_quota": 1000
     *     },
     *     "performance_metrics": {
     *       "quality_score": 9.5,
     *       "speed_score": 8.0,
     *       "creativity_score": 9.2,
     *       "average_response_time": "8.5秒"
     *     },
     *     "usage_statistics": {
     *       "total_uses": 15420,
     *       "user_rating": 4.8,
     *       "success_rate": 96.5
     *     },
     *     "user_access": {
     *       "is_available": true,
     *       "requires_subscription": true,
     *       "remaining_quota": 2500
     *     }
     *   }
     * })
     */
    public function detail($modelId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->modelService->getModelDetail($modelId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取模型详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取模型详情失败', []);
        }
    }

    /**
     * @ApiTitle(测试模型)
     * @ApiSummary(使用指定模型进行测试生成)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai-models/{model_id}/test)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="model_id", type="string", required=true, description="模型ID")
     * @ApiParams(name="prompt", type="string", required=true, description="测试提示词")
     * @ApiParams(name="parameters", type="object", required=false, description="模型参数")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "模型测试完成",
     *   "data": {
     *     "test_id": "test_123456",
     *     "model_id": "deepseek-story",
     *     "prompt": "写一个关于AI的故事",
     *     "result": {
     *       "content": "在不远的未来，人工智能已经...",
     *       "tokens_used": 245,
     *       "generation_time": 6.8,
     *       "quality_score": 8.5
     *     },
     *     "cost": {
     *       "tokens_consumed": 245,
     *       "cost_amount": 0.007,
     *       "currency": "USD"
     *     },
     *     "generated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function test($modelId, Request $request)
    {
        try {
            $rules = [
                'prompt' => 'required|string|min:1|max:2000',
                'parameters' => 'sometimes|array'
            ];

            try {
                $this->validateData($request->all(), $rules);
            } catch (\Illuminate\Validation\ValidationException $e) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
            }

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $testData = [
                'prompt' => $request->prompt,
                'parameters' => $request->get('parameters', [])
            ];

            $result = $this->modelService->testModel($modelId, $user->id, $testData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('模型测试失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '模型测试失败', []);
        }
    }

    /**
     * @ApiTitle(获取使用统计)
     * @ApiSummary(获取用户的AI模型使用统计)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/usage-stats)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：day/week/month/year")
     * @ApiParams(name="model_type", type="string", required=false, description="模型类型过滤")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "month",
     *     "total_usage": {
     *       "requests": 156,
     *       "tokens": 45230,
     *       "cost": 1.35,
     *       "success_rate": 97.4
     *     },
     *     "by_model": [
     *       {
     *         "model_id": "deepseek-story",
     *         "model_name": "DeepSeek 故事生成",
     *         "requests": 89,
     *         "tokens": 28450,
     *         "cost": 0.85,
     *         "success_rate": 98.9
     *       }
     *     ],
     *     "quotas": {
     *       "free_tokens": {
     *         "used": 7500,
     *         "limit": 10000,
     *         "remaining": 2500
     *       }
     *     }
     *   }
     * })
     */
    public function usageStats(Request $request)
    {
        try {
            $rules = [
                'period' => 'sometimes|string|in:day,week,month,year',
                'model_type' => 'sometimes|string|in:story,image,voice,video,music,sound'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $params = [
                'period' => $request->get('period', 'month'),
                'model_type' => $request->get('model_type')
            ];

            $result = $this->modelService->getUserUsageStats($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取使用统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取使用统计失败', []);
        }
    }

    /**
     * @ApiTitle(收藏模型)
     * @ApiSummary(收藏或取消收藏AI模型)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai-models/{model_id}/favorite)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="model_id", type="string", required=true, description="模型ID")
     * @ApiParams(name="action", type="string", required=true, description="操作类型：favorite/unfavorite")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "收藏操作成功",
     *   "data": {
     *     "model_id": "deepseek-story",
     *     "action": "favorite",
     *     "is_favorited": true,
     *     "favorite_count": 1251,
     *     "favorited_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function favorite($modelId, Request $request)
    {
        try {
            $rules = [
                'action' => 'required|string|in:favorite,unfavorite'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['message'],
                    $authResult['response']['code'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];

            $favoriteData = [
                'action' => $request->action
            ];

            $result = $this->modelService->manageFavorite($modelId, $user->id, $favoriteData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('收藏操作失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '收藏操作失败', []);
        }
    }

    /**
     * @ApiTitle(获取收藏模型)
     * @ApiSummary(获取用户收藏的AI模型列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/favorites)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="模型类型过滤")
     * @ApiParams(name="sort", type="string", required=false, description="排序方式：recent/name/usage")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "favorites": [
     *       {
     *         "model_id": "deepseek-story",
     *         "name": "DeepSeek 故事生成",
     *         "type": "story",
     *         "category": "premium",
     *         "user_rating": 4.8,
     *         "usage_count": 45,
     *         "last_used": "2024-01-01 10:30:00",
     *         "favorited_at": "2023-12-15 14:20:00"
     *       }
     *     ],
     *     "total_favorites": 8
     *   }
     * })
     */
    public function favorites(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:story,image,voice,video,music,sound',
                'sort' => 'sometimes|string|in:recent,name,usage,rating'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $params = [
                'type' => $request->get('type'),
                'sort' => $request->get('sort', 'recent')
            ];

            $result = $this->modelService->getUserFavorites($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取收藏列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取收藏列表失败', []);
        }
    }

    /**
     * 获取AI模型列表
     * 修复500错误 - 添加缺失的list方法
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request)
    {
        try {
            // 验证用户身份（可选）
            $userId = 0;
            $authResult = AuthService::authenticate($request);
            if ($authResult['success']) {
                $userId = $authResult['user']->id;
            }

            // 获取AI模型列表
            $result = $this->modelService->getAvailableModels($userId, [
                'service_type' => $request->input('service_type', 'all'),
                'status' => 'active'
            ]);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取AI模型列表失败', [
                'method' => __METHOD__,
                'user_id' => $userId ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI模型列表失败');
        }
    }

    /**
     * 切换AI模型
     * 修复500错误 - 添加缺失的switch方法
     *
     * @param Request $request
     * @return array
     */
    public function switch(Request $request)
    {
        try {
            // 验证请求参数
            $this->validateData($request->all(), [
                'model_id' => 'required|integer',
                'service_type' => 'required|string|in:text,image,audio,video,text_generation,image_generation,voice_synthesis,video_generation'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        }

        // 验证用户身份
        $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        $user = $authResult['user'];

        try {
            // 切换AI模型
            $result = $this->modelService->switchUserModel(
                $user->id,
                $request->input('model_id'),
                $request->input('service_type')
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], 'AI模型切换成功');
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('切换模型失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '切换模型失败');
        }
    }

    /**
     * @ApiTitle(智能平台选择)
     * @ApiSummary(根据业务类型和用户偏好智能选择最佳AI平台)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai-models/select-platform)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token")
     * @ApiParams(name="business_type", type="string", required=true, description="业务类型：image/video/story/character/style/voice/sound/music")
     * @ApiParams(name="criteria", type="object", required=false, description="选择标准配置")
     */
    public function selectOptimalPlatform(Request $request)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'business_type' => 'required|string|in:image,video,story,character,style,voice,sound,music',
                'criteria' => 'sometimes|array'
            ]);

            $result = $this->platformSelectionService->selectOptimalPlatform(
                $request->input('business_type'),
                $user->id,
                $request->input('criteria', [])
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('平台选择失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '平台选择失败');
        }
    }

    /**
     * @ApiTitle(平台健康检查)
     * @ApiSummary(检查指定AI平台的健康状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/platform-health/{platform})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token")
     */
    public function checkPlatformHealth(string $platform)
    {
        try {
            $healthStatus = $this->platformHealthService->checkPlatformHealth($platform);
            return $this->successResponse($healthStatus, '平台健康检查完成');

        } catch (\Exception $e) {
            Log::error('健康检查失败', [
                'method' => __METHOD__,
                'platform' => $platform,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '健康检查失败');
        }
    }

    /**
     * @ApiTitle(所有平台健康状态)
     * @ApiSummary(获取所有AI平台的健康状态摘要)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/platforms-health)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token")
     */
    public function getAllPlatformsHealth()
    {
        try {
            $healthSummary = $this->platformHealthService->getHealthSummary();
            return $this->successResponse($healthSummary, '获取平台健康状态成功');

        } catch (\Exception $e) {
            Log::error('获取健康状态失败', [
                'method' => __METHOD__,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取健康状态失败');
        }
    }

    /**
     * @ApiTitle(平台性能统计)
     * @ApiSummary(获取指定平台的性能统计数据)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/platform-stats/{platform})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token")
     * @ApiParams(name="days", type="integer", required=false, description="统计天数，默认7天")
     */
    public function getPlatformStats(Request $request, string $platform)
    {
        try {
            $days = $request->input('days', 7);
            $stats = $this->platformHealthService->getPlatformAvailability($platform, $days);
            return $this->successResponse($stats, '获取平台统计数据成功');

        } catch (\Exception $e) {
            Log::error('获取统计数据失败', [
                'method' => __METHOD__,
                'platform' => $platform,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取统计数据失败');
        }
    }

    /**
     * @ApiTitle(平台性能对比)
     * @ApiSummary(获取AI平台性能指标对比，帮助用户选择最适合的平台)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/platform-comparison)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="business_type", type="string", required=false, description="业务类型筛选：image/video/story/character/style/sound/voice/music")
     * @ApiParams(name="sort_by", type="string", required=false, description="排序字段：response_time/success_rate/cost/quality")
     * @ApiParams(name="order", type="string", required=false, description="排序方向：asc/desc，默认asc")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="平台对比数据")
     * @ApiReturnParams (name="data['platforms']", type="array", required=true, description="平台列表")
     * @ApiReturnParams (name="data['recommendations']", type="array", required=true, description="推荐建议")
     * @ApiReturnParams (name="data['last_updated']", type="string", required=true, description="最后更新时间")
     * @ApiReturn({
        "code": 200,
        "message": "平台对比数据获取成功",
        "data": {
            "platforms": [
                {
                    "id": 1,
                    "name": "LiblibAI",
                    "business_type": "image",
                    "response_time": 2.8,
                    "success_rate": 96.5,
                    "cost_per_request": 0.03,
                    "quality_score": 8.8,
                    "status": "active"
                }
            ],
            "recommendations": [
                {
                    "platform_id": 3,
                    "reason": "最快响应时间和高成功率，适合批量图像生成",
                    "score": 9.1
                }
            ],
            "last_updated": "2025-07-30T10:45:00Z"
        }
        })
     */
    public function platformComparison(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '请登录后操作');
            }

            // 参数验证
            $rules = [
                'business_type' => 'sometimes|string|in:image,video,story,character,style,sound,voice,music',
                'sort_by' => 'sometimes|string|in:response_time,success_rate,cost,quality',
                'order' => 'sometimes|string|in:asc,desc'
            ];

            $messages = [
                'business_type.in' => '业务类型必须是：image,video,story,character,style,sound,voice,music之一',
                'sort_by.in' => '排序字段必须是：response_time,success_rate,cost,quality之一',
                'order.in' => '排序方向必须是：asc,desc之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 获取筛选参数
            $filters = [
                'business_type' => $request->input('business_type'),
                'sort_by' => $request->input('sort_by', 'response_time'),
                'order' => $request->input('order', 'asc')
            ];

            // 调用平台健康服务获取对比数据
            $result = $this->platformHealthService->getPlatformComparison($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('获取平台对比数据失败', [
                'method' => __METHOD__,
                'user_id' => $authResult['user']->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取平台对比数据失败');
        }
    }

    /**
     * @ApiTitle(按业务类型获取平台)
     * @ApiSummary(根据业务类型返回可用AI平台列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai-models/business-platforms)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="business_type", type="string", required=true, description="业务类型：image/video/story/character/style/sound/voice/music")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="平台列表数据")
     * @ApiReturn({
        "code": 200,
        "message": "获取平台列表成功",
        "data": {
            "business_type": "image",
            "platforms": [
                {
                    "id": 1,
                    "name": "LiblibAI",
                    "business_type": "image",
                    "status": "active"
                }
            ],
            "total_count": 3
        }
        })
     */
    public function businessPlatforms(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '请登录后操作');
            }

            // 参数验证
            $rules = [
                'business_type' => 'required|string|in:image,video,story,character,style,sound,voice,music'
            ];

            $messages = [
                'business_type.required' => '业务类型不能为空',
                'business_type.in' => '业务类型必须是：image,video,story,character,style,sound,voice,music之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 获取业务类型参数
            $businessType = $request->input('business_type');

            // 调用平台健康服务获取业务平台列表
            $result = $this->platformHealthService->getBusinessPlatforms($businessType);

            if ($result['code'] === 200) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('获取平台列表失败', [
                'method' => __METHOD__,
                'user_id' => $authResult['user']->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取平台列表失败');
        }
    }
}
