<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use App\Models\Resource;
use App\Models\ResourceVersion;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * 版本控制服务
 * 第3A阶段：版本控制系统模块
 */
class VersionControlService extends Service
{
    protected $resourceService;
    protected $pointsService;

    public function __construct(ResourceManagementService $resourceService, PointsService $pointsService)
    {
        $this->resourceService = $resourceService;
        $this->pointsService = $pointsService;
    }

    /**
     * 创建新版本
     */
    public function createVersion(int $userId, int $resourceId, array $versionParams): array
    {
        try {
            DB::beginTransaction();

            // 验证资源权限
            $resource = Resource::where('id', $resourceId)
                ->where('user_id', $userId)
                ->first();

            if (!$resource) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '资源不存在或无权限访问',
                    'data' => []
                ];
            }

            // 获取下一个版本号
            $nextVersionNumber = $this->getNextVersionNumber($resourceId);

            // 计算预估成本
            $estimatedCost = $this->calculateVersionCost($resource, $versionParams);

            // 检查积分余额
            $checkResult = $this->pointsService->checkPoints(
                $userId,
                $estimatedCost,
                'version_creation'
            );

            if ($checkResult['code'] !== ApiCodeEnum::SUCCESS || !$checkResult['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => $checkResult['data']
                ];
            }

            // 创建版本记录
            $version = ResourceVersion::create([
                'resource_id' => $resourceId,
                'user_id' => $userId,
                'version_number' => $nextVersionNumber,
                'version_name' => $versionParams['version_name'] ?? "版本 {$nextVersionNumber}",
                'description' => $versionParams['description'],
                'status' => ResourceVersion::STATUS_PENDING,
                'generation_config' => $versionParams['generation_config'],
                'base_version_id' => $versionParams['base_version_id'],
                'estimated_cost' => $estimatedCost,
                'metadata' => [
                    'created_by' => 'version_control_service',
                    'resource_type' => $resource->resource_type
                ]
            ]);

            // 创建生成任务
            $generationParams = array_merge($versionParams['generation_config'], [
                'resource_type' => $resource->resource_type,
                'quality_level' => $resource->quality_level,
                'output_format' => $resource->output_format
            ]);

            $generationResult = $this->resourceService->createGenerationTask(
                $userId,
                $resource->project_id,
                $generationParams
            );

            if ($generationResult['code'] !== ApiCodeEnum::SUCCESS) {
                DB::rollBack();
                return $generationResult;
            }

            // 更新版本记录
            $version->update([
                'generation_task_id' => $generationResult['data']['generation_task_id'],
                'status' => ResourceVersion::STATUS_PROCESSING
            ]);

            DB::commit();

            Log::info('版本创建成功', [
                'version_id' => $version->id,
                'resource_id' => $resourceId,
                'user_id' => $userId,
                'version_number' => $nextVersionNumber,
                'estimated_cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '版本创建成功',
                'data' => [
                    'version_id' => $version->id,
                    'resource_id' => $resourceId,
                    'version_number' => $nextVersionNumber,
                    'version_name' => $version->version_name,
                    'status' => $version->status,
                    'generation_task_id' => $generationResult['data']['generation_task_id'],
                    'estimated_cost' => $estimatedCost,
                    'created_at' => $version->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'resource_id' => $resourceId,
                'version_name' => $versionParams['version_name'] ?? null,
                'description' => $versionParams['description'] ?? null,
            ];

            Log::error('版本创建失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '版本创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取版本历史
     */
    public function getVersionHistory(int $userId, int $resourceId, array $filters): array
    {
        try {
            // 验证资源权限
            $resource = Resource::where('id', $resourceId)
                ->where('user_id', $userId)
                ->first();

            if (!$resource) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '资源不存在或无权限访问',
                    'data' => []
                ];
            }

            $query = ResourceVersion::where('resource_id', $resourceId);

            // 应用过滤条件
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $versions = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $versionList = [];
            foreach ($versions->items() as $version) {
                $versionList[] = [
                    'version_id' => $version->id,
                    'version_number' => $version->version_number,
                    'version_name' => $version->version_name,
                    'description' => $version->description,
                    'status' => $version->status,
                    'file_url' => $version->file_url,
                    'file_size' => $version->formatted_file_size,
                    'cost' => $version->actual_cost ?? $version->estimated_cost,
                    'created_at' => $version->created_at->format('Y-m-d H:i:s'),
                    'completed_at' => $version->completed_at ? $version->completed_at->format('Y-m-d H:i:s') : null,
                    'is_current' => $version->is_current
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'resource_id' => $resourceId,
                    'versions' => $versionList,
                    'pagination' => [
                        'current_page' => $versions->currentPage(),
                        'per_page' => $versions->perPage(),
                        'total' => $versions->total(),
                        'last_page' => $versions->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取版本历史失败', [
                'user_id' => $userId,
                'resource_id' => $resourceId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取版本历史失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取版本详情
     */
    public function getVersionDetail(int $userId, int $versionId): array
    {
        try {
            $version = ResourceVersion::with(['resource', 'baseVersion'])
                ->where('id', $versionId)
                ->where('user_id', $userId)
                ->first();

            if (!$version) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '版本不存在或无权限访问',
                    'data' => []
                ];
            }

            $data = [
                'version_id' => $version->id,
                'resource_id' => $version->resource_id,
                'version_number' => $version->version_number,
                'version_name' => $version->version_name,
                'description' => $version->description,
                'status' => $version->status,
                'generation_config' => $version->generation_config,
                'file_info' => [
                    'file_url' => $version->file_url,
                    'file_size' => $version->formatted_file_size,
                    'file_hash' => $version->file_hash
                ],
                'cost' => $version->actual_cost ?? $version->estimated_cost,
                'processing_time_ms' => $version->processing_time_ms,
                'created_at' => $version->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $version->completed_at ? $version->completed_at->format('Y-m-d H:i:s') : null,
                'base_version' => $version->baseVersion ? [
                    'version_id' => $version->baseVersion->id,
                    'version_number' => $version->baseVersion->version_number
                ] : null
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取版本详情失败', [
                'user_id' => $userId,
                'version_id' => $versionId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取版本详情失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 设置当前版本
     */
    public function setCurrentVersion(int $userId, int $versionId): array
    {
        try {
            DB::beginTransaction();

            $version = ResourceVersion::where('id', $versionId)
                ->where('user_id', $userId)
                ->first();

            if (!$version) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '版本不存在或无权限访问',
                    'data' => []
                ];
            }

            if ($version->status !== ResourceVersion::STATUS_COMPLETED) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '只能设置已完成的版本为当前版本',
                    'data' => []
                ];
            }

            // 取消其他版本的当前状态
            ResourceVersion::where('resource_id', $version->resource_id)
                ->update(['is_current' => false]);

            // 设置当前版本
            $version->update(['is_current' => true]);

            DB::commit();

            Log::info('当前版本设置成功', [
                'version_id' => $versionId,
                'resource_id' => $version->resource_id,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '当前版本设置成功',
                'data' => [
                    'version_id' => $version->id,
                    'resource_id' => $version->resource_id,
                    'version_number' => $version->version_number,
                    'is_current' => true
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置当前版本失败', [
                'user_id' => $userId,
                'version_id' => $versionId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '设置当前版本失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 删除版本
     */
    public function deleteVersion(int $userId, int $versionId): array
    {
        try {
            DB::beginTransaction();

            $version = ResourceVersion::where('id', $versionId)
                ->where('user_id', $userId)
                ->first();

            if (!$version) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '版本不存在或无权限访问',
                    'data' => []
                ];
            }

            if ($version->is_current) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '不能删除当前版本',
                    'data' => []
                ];
            }

            // 删除相关文件
            $deletedFiles = 0;
            $freedSpace = 0;

            if ($version->file_path) {
                if (Storage::exists($version->file_path)) {
                    $fileSize = Storage::size($version->file_path);
                    Storage::delete($version->file_path);
                    $deletedFiles++;
                    $freedSpace += $fileSize;
                }
            }

            // 删除版本记录
            $version->delete();

            DB::commit();

            Log::info('版本删除成功', [
                'version_id' => $versionId,
                'user_id' => $userId,
                'deleted_files' => $deletedFiles,
                'freed_space' => $freedSpace
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '版本删除成功',
                'data' => [
                    'version_id' => $versionId,
                    'deleted_files' => $deletedFiles,
                    'freed_space' => $this->formatFileSize($freedSpace)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('版本删除失败', [
                'user_id' => $userId,
                'version_id' => $versionId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '版本删除失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 比较版本
     */
    public function compareVersions(int $userId, int $version1Id, int $version2Id): array
    {
        try {
            $version1 = ResourceVersion::where('id', $version1Id)
                ->where('user_id', $userId)
                ->first();

            $version2 = ResourceVersion::where('id', $version2Id)
                ->where('user_id', $userId)
                ->first();

            if (!$version1 || !$version2) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '版本不存在或无权限访问',
                    'data' => []
                ];
            }

            if ($version1->resource_id !== $version2->resource_id) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '只能比较同一资源的不同版本',
                    'data' => []
                ];
            }

            // 比较配置差异
            $configChanges = $this->compareConfigs(
                $version1->generation_config,
                $version2->generation_config
            );

            // 比较文件差异
            $fileChanges = $this->compareFiles($version1, $version2);

            $data = [
                'version1' => [
                    'version_id' => $version1->id,
                    'version_number' => $version1->version_number,
                    'created_at' => $version1->created_at->format('Y-m-d H:i:s')
                ],
                'version2' => [
                    'version_id' => $version2->id,
                    'version_number' => $version2->version_number,
                    'created_at' => $version2->created_at->format('Y-m-d H:i:s')
                ],
                'differences' => [
                    'config_changes' => $configChanges,
                    'file_changes' => $fileChanges
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '版本比较完成',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('版本比较失败', [
                'user_id' => $userId,
                'version1_id' => $version1Id,
                'version2_id' => $version2Id,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '版本比较失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取下一个版本号
     */
    private function getNextVersionNumber(int $resourceId): string
    {
        $latestVersion = ResourceVersion::where('resource_id', $resourceId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$latestVersion) {
            return 'v1.0';
        }

        // 简单的版本号递增逻辑
        $versionNumber = $latestVersion->version_number;
        if (preg_match('/v(\d+)\.(\d+)/', $versionNumber, $matches)) {
            $major = (int)$matches[1];
            $minor = (int)$matches[2];
            $minor++;
            return "v{$major}.{$minor}";
        }

        return 'v1.0';
    }

    /**
     * 计算版本成本
     */
    private function calculateVersionCost(Resource $resource, array $params): float
    {
        // 版本创建成本为原始成本的80%
        return round($resource->estimated_cost * 0.8, 4);
    }

    /**
     * 比较配置差异
     */
    private function compareConfigs(array $config1, array $config2): array
    {
        $changes = [];
        
        foreach ($config2 as $key => $value) {
            if (!isset($config1[$key]) || $config1[$key] !== $value) {
                $changes[] = [
                    'field' => $key,
                    'old_value' => $config1[$key] ?? null,
                    'new_value' => $value
                ];
            }
        }

        return $changes;
    }

    /**
     * 比较文件差异
     */
    private function compareFiles(ResourceVersion $version1, ResourceVersion $version2): array
    {
        $sizeDiff = ($version2->file_size ?? 0) - ($version1->file_size ?? 0);
        
        return [
            'size_diff' => $sizeDiff > 0 ? '+' . $this->formatFileSize($sizeDiff) : $this->formatFileSize($sizeDiff),
            'quality_improved' => $sizeDiff > 0 // 简单假设文件更大意味着质量更好
        ];
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . $units[$pow];
    }
}
