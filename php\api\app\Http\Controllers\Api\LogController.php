<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 日志管理控制器
 * 处理系统日志查询、用户操作日志、AI调用日志等
 */
class LogController extends Controller
{
    protected $logService;

    public function __construct(LogService $logService)
    {
        $this->logService = $logService;
    }

    /**
     * @ApiTitle(查询系统日志)
     * @ApiSummary(查询系统运行日志)
     * @ApiMethod(GET)
     * @ApiRoute(/api/logs/system)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="level", type="string", required=false, description="日志级别：debug,info,warning,error,critical")
     * @ApiParams(name="component", type="string", required=false, description="组件名称：api,ai,database,cache,queue")
     * @ApiParams(name="start_time", type="string", required=false, description="开始时间：2024-01-01 00:00:00")
     * @ApiParams(name="end_time", type="string", required=false, description="结束时间：2024-01-01 23:59:59")
     * @ApiParams(name="keyword", type="string", required=false, description="关键词搜索")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "logs": [
     *       {
     *         "id": 1,
     *         "level": "error",
     *         "component": "ai",
     *         "message": "AI服务调用失败：连接超时",
     *         "context": {
     *           "platform": "liblib",
     *           "task_id": 123,
     *           "error_code": "TIMEOUT"
     *         },
     *         "ip": "*************",
     *         "user_agent": "TipTop-API/1.0",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "summary": {
     *       "total": 1250,
     *       "by_level": {
     *         "error": 15,
     *         "warning": 45,
     *         "info": 890,
     *         "debug": 300
     *       }
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 50,
     *       "total": 1250,
     *       "last_page": 25
     *     }
     *   }
     * })
     */
    public function systemLogs(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看系统日志');
            }

            $rules = [
                'level' => 'sometimes|string|in:debug,info,warning,error,critical',
                'component' => 'sometimes|string|in:api,ai,database,cache,queue,auth,payment',
                'start_time' => 'sometimes|date_format:Y-m-d H:i:s',
                'end_time' => 'sometimes|date_format:Y-m-d H:i:s|after:start_time',
                'keyword' => 'sometimes|string|max:200',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'level' => $request->get('level'),
                'component' => $request->get('component'),
                'start_time' => $request->get('start_time'),
                'end_time' => $request->get('end_time'),
                'keyword' => $request->get('keyword'),
                'page' => $request->get('page', 1),
                'per_page' => 50
            ];

            $result = $this->logService->getSystemLogs($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('查询系统日志失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '查询系统日志失败');
        }
    }

    /**
     * @ApiTitle(查询用户操作日志)
     * @ApiSummary(查询用户操作行为日志)
     * @ApiMethod(GET)
     * @ApiRoute(/api/logs/user-actions)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=false, description="用户ID")
     * @ApiParams(name="action", type="string", required=false, description="操作类型：login,logout,generate,upload,download")
     * @ApiParams(name="start_time", type="string", required=false, description="开始时间")
     * @ApiParams(name="end_time", type="string", required=false, description="结束时间")
     * @ApiParams(name="ip", type="string", required=false, description="IP地址")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "logs": [
     *       {
     *         "id": 1,
     *         "user_id": 123,
     *         "username": "testuser",
     *         "action": "generate",
     *         "resource": "image",
     *         "description": "生成AI图像",
     *         "details": {
     *           "prompt": "一只可爱的小猫",
     *           "style": "cartoon",
     *           "platform": "liblib"
     *         },
     *         "ip": "*************",
     *         "user_agent": "Mozilla/5.0...",
     *         "status": "success",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "summary": {
     *       "total": 5420,
     *       "by_action": {
     *         "login": 890,
     *         "generate": 3200,
     *         "upload": 850,
     *         "download": 480
     *       }
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 50,
     *       "total": 5420,
     *       "last_page": 109
     *     }
     *   }
     * })
     */
    public function userActionLogs(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查权限：管理员可查看所有用户日志，普通用户只能查看自己的日志
            $canViewAll = $user->is_admin;
            $targetUserId = $request->get('user_id');

            if (!$canViewAll && $targetUserId && $targetUserId != $user->id) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，只能查看自己的操作日志');
            }

            $rules = [
                'user_id' => 'sometimes|integer|exists:users,id',
                'action' => 'sometimes|string|in:login,logout,register,generate,upload,download,delete,update',
                'start_time' => 'sometimes|date_format:Y-m-d H:i:s',
                'end_time' => 'sometimes|date_format:Y-m-d H:i:s|after:start_time',
                'ip' => 'sometimes|ip',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'user_id' => $canViewAll ? $targetUserId : $user->id,
                'action' => $request->get('action'),
                'start_time' => $request->get('start_time'),
                'end_time' => $request->get('end_time'),
                'ip' => $request->get('ip'),
                'page' => $request->get('page', 1),
                'per_page' => 50
            ];

            $result = $this->logService->getUserActionLogs($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('查询用户操作日志失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '查询用户操作日志失败');
        }
    }

    /**
     * @ApiTitle(查询AI调用日志)
     * @ApiSummary(查询AI服务调用日志)
     * @ApiMethod(GET)
     * @ApiRoute(/api/logs/ai-calls)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="platform", type="string", required=false, description="AI平台：liblib,deepseek,kling,minimax")
     * @ApiParams(name="type", type="string", required=false, description="调用类型：image,story,video,voice,music")
     * @ApiParams(name="status", type="string", required=false, description="调用状态：success,failed,timeout")
     * @ApiParams(name="start_time", type="string", required=false, description="开始时间")
     * @ApiParams(name="end_time", type="string", required=false, description="结束时间")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "logs": [
     *       {
     *         "id": 1,
     *         "platform": "liblib",
     *         "type": "image",
     *         "task_id": 123,
     *         "user_id": 456,
     *         "request_data": {
     *           "prompt": "一只可爱的小猫",
     *           "style": "cartoon"
     *         },
     *         "response_data": {
     *           "image_url": "https://api.tiptop.cn/files/123.jpg",
     *           "generation_time": 45
     *         },
     *         "status": "success",
     *         "response_time": 2500,
     *         "cost": 10,
     *         "error_message": null,
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "summary": {
     *       "total": 8520,
     *       "success_rate": 96.5,
     *       "average_response_time": 2800,
     *       "by_platform": {
     *         "liblib": {"calls": 3200, "success_rate": 98.1},
     *         "deepseek": {"calls": 2100, "success_rate": 97.8}
     *       }
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 50,
     *       "total": 8520,
     *       "last_page": 171
     *     }
     *   }
     * })
     */
    public function aiCallLogs(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看AI调用日志');
            }

            $rules = [
                'platform' => 'sometimes|string|in:liblib,deepseek,kling,minimax,douyin',
                'type' => 'sometimes|string|in:image,story,video,voice,music,sound',
                'status' => 'sometimes|string|in:success,failed,timeout,cancelled',
                'start_time' => 'sometimes|date_format:Y-m-d H:i:s',
                'end_time' => 'sometimes|date_format:Y-m-d H:i:s|after:start_time',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'platform' => $request->get('platform'),
                'type' => $request->get('type'),
                'status' => $request->get('status'),
                'start_time' => $request->get('start_time'),
                'end_time' => $request->get('end_time'),
                'page' => $request->get('page', 1),
                'per_page' => 50
            ];

            $result = $this->logService->getAiCallLogs($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('AI调用日志查询失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, 'AI调用日志查询失败');
        }
    }

    /**
     * @ApiTitle(查询错误日志)
     * @ApiSummary(查询系统错误和异常日志)
     * @ApiMethod(GET)
     * @ApiRoute(/api/logs/errors)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="level", type="string", required=false, description="错误级别：error,critical")
     * @ApiParams(name="component", type="string", required=false, description="组件名称")
     * @ApiParams(name="start_time", type="string", required=false, description="开始时间")
     * @ApiParams(name="end_time", type="string", required=false, description="结束时间")
     * @ApiParams(name="resolved", type="boolean", required=false, description="是否已解决")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "logs": [
     *       {
     *         "id": 1,
     *         "level": "error",
     *         "component": "ai",
     *         "message": "AI服务调用失败",
     *         "exception": "ConnectionTimeoutException",
     *         "stack_trace": "...",
     *         "context": {
     *           "platform": "liblib",
     *           "task_id": 123
     *         },
     *         "count": 5,
     *         "first_seen": "2024-01-01 12:00:00",
     *         "last_seen": "2024-01-01 12:30:00",
     *         "resolved": false,
     *         "resolved_at": null,
     *         "resolved_by": null
     *       }
     *     ],
     *     "summary": {
     *       "total": 156,
     *       "unresolved": 23,
     *       "by_component": {
     *         "ai": 45,
     *         "database": 12,
     *         "api": 89,
     *         "cache": 10
     *       }
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 50,
     *       "total": 156,
     *       "last_page": 4
     *     }
     *   }
     * })
     */
    public function errorLogs(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看错误日志');
            }

            $rules = [
                'level' => 'sometimes|string|in:error,critical',
                'component' => 'sometimes|string|in:api,ai,database,cache,queue,auth,payment',
                'start_time' => 'sometimes|date_format:Y-m-d H:i:s',
                'end_time' => 'sometimes|date_format:Y-m-d H:i:s|after:start_time',
                'resolved' => 'sometimes|boolean',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'level' => $request->get('level'),
                'component' => $request->get('component'),
                'start_time' => $request->get('start_time'),
                'end_time' => $request->get('end_time'),
                'resolved' => $request->get('resolved'),
                'page' => $request->get('page', 1),
                'per_page' => 50
            ];

            $result = $this->logService->getErrorLogs($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('错误日志获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '错误日志获取失败');
        }
    }

    /**
     * @ApiTitle(标记错误为已解决)
     * @ApiSummary(标记错误日志为已解决状态)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/logs/errors/{id}/resolve)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="错误日志ID")
     * @ApiParams(name="solution", type="string", required=false, description="解决方案描述")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "错误已标记为解决",
     *   "data": {
     *     "error_id": 1,
     *     "resolved": true,
     *     "resolved_by": "admin",
     *     "resolved_at": "2024-01-01 12:00:00",
     *     "solution": "优化了AI服务连接池配置"
     *   }
     * })
     */
    public function resolveError($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可解决错误');
            }

            $rules = [
                'solution' => 'sometimes|string|max:1000'
            ];

            $this->validateData($request->all(), $rules);

            $solution = $request->get('solution', '', []);

            $result = $this->logService->resolveError($id, $user->id, $solution);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('错误解决失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '错误解决失败');
        }
    }

    /**
     * @ApiTitle(导出日志)
     * @ApiSummary(导出指定条件的日志数据)
     * @ApiMethod(POST)
     * @ApiRoute(/api/logs/export)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=true, description="日志类型：system,user_actions,ai_calls,errors")
     * @ApiParams(name="format", type="string", required=false, description="导出格式：csv,json,xlsx")
     * @ApiParams(name="filters", type="object", required=false, description="筛选条件")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "日志导出任务创建成功",
     *   "data": {
     *     "export_id": "export_123456",
     *     "estimated_time": 120,
     *     "download_url": null,
     *     "status": "processing"
     *   }
     * })
     */
    public function exportLogs(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可导出日志');
            }

            $rules = [
                'type' => 'required|string|in:system,user_actions,ai_calls,errors',
                'format' => 'sometimes|string|in:csv,json,xlsx',
                'filters' => 'sometimes|array'
            ];

            $messages = [
                'type.required' => '日志类型不能为空',
                'type.in' => '不支持的日志类型'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $exportData = [
                'type' => $request->type,
                'format' => $request->get('format', 'csv'),
                'filters' => $request->get('filters', []),
                'user_id' => $user->id
            ];

            $result = $this->logService->exportLogs($exportData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('日志导出失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '日志导出失败');
        }
    }
}
