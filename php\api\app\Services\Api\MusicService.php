<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 音乐生成服务
 * 第2D3阶段：音乐生成模块
 */
class MusicService extends Service
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成音乐
     */
    public function generateMusic(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'minimax';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_MUSIC_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的音乐生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '音乐生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildMusicPrompt($prompt, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateMusicCost($model, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'music_generation',
                null,
                900 // 15分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_MUSIC_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'genre' => $generationParams['genre'] ?? 'pop',
                    'mood' => $generationParams['mood'] ?? 'happy',
                    'duration' => $generationParams['duration'] ?? 60,
                    'tempo' => $generationParams['tempo'] ?? 'medium',
                    'instruments' => $generationParams['instruments'] ?? []
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeMusicGeneration($task);

            Log::info('音乐生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音乐生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'estimated_duration' => $this->getEstimatedDuration($generationParams),
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('音乐生成失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音乐生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音乐生成状态
     */
    public function getMusicStatus(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_MUSIC_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        $data = [
            'id' => $task->id,
            'task_type' => $task->task_type,
            'status' => $task->status,
            'platform' => $task->platform,
            'cost' => $task->cost,
            'processing_time_ms' => $task->processing_time_ms,
            'created_at' => $task->created_at->format('Y-m-d H:i:s'),
            'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
        ];

        // 如果任务完成，添加生成结果
        if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
            $data['audio_url'] = $task->output_data['audio_url'] ?? '';
            $data['duration'] = $task->output_data['duration'] ?? 0;
            $data['file_size'] = $task->output_data['file_size'] ?? '';
        }

        // 如果任务失败，添加错误信息
        if ($task->status === AiGenerationTask::STATUS_FAILED) {
            $data['error_message'] = $task->error_message;
        }

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 获取音乐生成结果
     */
    public function getMusicResult(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_MUSIC_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        if ($task->status !== AiGenerationTask::STATUS_COMPLETED) {
            return [
                'code' => ApiCodeEnum::FAIL,
                'message' => '任务尚未完成',
                'data' => []
            ];
        }

        $data = [
            'task_id' => $task->id,
            'audio_url' => $task->output_data['audio_url'] ?? '',
            'waveform_url' => $task->output_data['waveform_url'] ?? '',
            'metadata' => [
                'duration' => $task->output_data['duration'] ?? 0,
                'format' => 'mp3',
                'bitrate' => $task->output_data['bitrate'] ?? '320kbps',
                'sample_rate' => $task->output_data['sample_rate'] ?? '44100Hz',
                'file_size' => $task->output_data['file_size'] ?? '',
                'genre' => $task->input_data['genre'] ?? '',
                'mood' => $task->input_data['mood'] ?? ''
            ],
            'download_info' => [
                'direct_url' => $task->output_data['audio_url'] ?? '',
                'expires_at' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s')
            ]
        ];

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 批量生成音乐
     */
    public function batchGenerateMusic(int $userId, array $prompts, ?int $projectId = null, array $commonParams = []): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $taskIds = [];
            $totalCost = 0;

            foreach ($prompts as $prompt) {
                $result = $this->generateMusic($userId, $prompt, $projectId, $commonParams);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $taskIds[] = $result['data']['task_id'];
                    $totalCost += $result['data']['estimated_cost'];
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量音乐生成任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'task_ids' => $taskIds,
                    'total_count' => count($taskIds),
                    'estimated_cost' => number_format($totalCost, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量音乐生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量音乐生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 构建音乐提示词
     */
    private function buildMusicPrompt(string $prompt, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加风格信息
        if (!empty($params['genre'])) {
            $enhancedPrompt .= "\n\n音乐风格：" . $params['genre'];
        }

        // 添加情绪信息
        if (!empty($params['mood'])) {
            $enhancedPrompt .= "\n音乐情绪：" . $params['mood'];
        }

        // 添加节拍信息
        if (!empty($params['tempo'])) {
            $tempoMap = [
                'slow' => '慢节拍（60-80 BPM）',
                'medium' => '中等节拍（80-120 BPM）',
                'fast' => '快节拍（120-160 BPM）'
            ];
            $enhancedPrompt .= "\n节拍：" . ($tempoMap[$params['tempo']] ?? $params['tempo']);
        }

        // 添加乐器信息
        if (!empty($params['instruments'])) {
            $enhancedPrompt .= "\n乐器：" . implode('、', $params['instruments']);
        }

        // 添加时长要求
        $duration = $params['duration'] ?? 60;
        $enhancedPrompt .= "\n\n音乐时长：{$duration}秒";

        return $enhancedPrompt;
    }

    /**
     * 计算音乐生成成本
     */
    private function calculateMusicCost(AiModelConfig $model, array $params): float
    {
        $baseCost = $model->cost_per_token;
        
        // 时长影响成本
        $duration = $params['duration'] ?? 60;
        $durationMultiplier = $duration / 60; // 以60秒为基准
        
        // 复杂度影响成本
        $complexity = 1.0;
        if (!empty($params['instruments']) && count($params['instruments']) > 3) {
            $complexity = 1.5; // 多乐器增加复杂度
        }

        return round($baseCost * $durationMultiplier * $complexity, 4);
    }

    /**
     * 获取预估生成时间
     */
    private function getEstimatedDuration(array $params): int
    {
        $duration = $params['duration'] ?? 60;
        
        // 基础生成时间（秒）
        $baseTime = $duration * 2; // 每秒音乐需要2秒生成时间
        
        // 复杂度影响生成时间
        $complexity = 1.0;
        if (!empty($params['instruments']) && count($params['instruments']) > 3) {
            $complexity = 1.5;
        }

        return (int)($baseTime * $complexity);
    }

    /**
     * 执行音乐生成
     */
    private function executeMusicGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'music_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'music_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'music_generation_error', $task->id);

            Log::error('音乐生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/minimax/v1/music_generation';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 60);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'prompt' => $task->input_data['enhanced_prompt'],
                'genre' => $task->input_data['genre'],
                'mood' => $task->input_data['mood'],
                'duration' => $task->input_data['duration'],
                'tempo' => $task->input_data['tempo'],
                'instruments' => $task->input_data['instruments']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'data' => [
                        'audio_url' => $data['data']['audio_url'] ?? '',
                        'waveform_url' => $data['data']['waveform_url'] ?? '',
                        'duration' => $data['data']['duration'] ?? $task->input_data['duration'],
                        'file_size' => $data['data']['file_size'] ?? '3.2MB',
                        'bitrate' => $data['data']['bitrate'] ?? '320kbps',
                        'sample_rate' => $data['data']['sample_rate'] ?? '44100Hz'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }
}
