<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Models\User;
use Carbon\Carbon;
use App\Models\PointsTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

/**
 * 积分交易服务类
 * 实现积分冻结、扣取、返还的完整流程
 * 基于index.mdc和dev-api-guidelines-new.mdc的规范
 */
class PointsTransactionService extends Service
{
    /**
     * 创建冻结积分记录
     * 
     * @param int $userId 用户ID
     * @param array $businessInfo 业务信息
     * @return array
     */
    public function createFrozenTransaction($userId, $businessInfo)
    {
        try {
            return DB::transaction(function() use ($userId, $businessInfo) {
                // 1. 检查积分余额
                $user = User::lockForUpdate()->find($userId);
                if (!$user) {
                    return ['success' => false, 'message' => '用户不存在'];
                }

                if ($user->points < $businessInfo['cost']) {
                    return ['success' => false, 'message' => '积分不足'];
                }

                // 2. 扣取积分并冻结
                $user->decrement('points', $businessInfo['cost']);
                $user->increment('frozen_points', $businessInfo['cost']);

                // 3. 创建业务日志记录
                $transaction = PointsTransaction::create([
                    'user_id' => $userId,
                    'business_type' => $businessInfo['type'],
                    'business_id' => $businessInfo['id'],
                    'amount' => $businessInfo['cost'],
                    'status' => 'frozen',
                    'ai_platform' => $businessInfo['platform'],
                    'request_data' => json_encode($businessInfo['request']),
                    'timeout_seconds' => $this->getTimeoutByType($businessInfo['type']),
                    'created_at' => Carbon::now()
                ]);

                // 4. 同步到Redis缓存
                Redis::setex(
                    "transaction:{$transaction->id}",
                    3600,
                    json_encode($transaction->toArray())
                );

                Log::info('积分冻结成功', [
                    'user_id' => $userId,
                    'transaction_id' => $transaction->id,
                    'amount' => $businessInfo['cost']
                ]);

                return [
                    'success' => true,
                    'transaction_id' => $transaction->id,
                    'frozen_amount' => $businessInfo['cost'],
                    'remaining_points' => $user->fresh()->points
                ];
            });

        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'business_type' => $businessInfo['type'] ?? null,
                'business_id' => $businessInfo['id'] ?? null,
                'cost' => $businessInfo['cost'] ?? null,
            ];

            Log::error('创建冻结交易失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '创建冻结交易失败'
            ];
        }
    }

    /**
     * 处理成功完成
     * 
     * @param int $transactionId 交易ID
     * @param array $result 结果数据
     * @return array
     */
    public function handleSuccess($transactionId, $result)
    {
        return DB::transaction(function() use ($transactionId, $result) {
            $transaction = PointsTransaction::find($transactionId);
            if (!$transaction || $transaction->status !== 'frozen') {
                return ['success' => false, 'message' => '交易状态异常'];
            }

            // 更新交易状态
            $transaction->update([
                'status' => 'success',
                'response_data' => json_encode($result),
                'completed_at' => Carbon::now()
            ]);

            // 从用户冻结积分中扣除
            $user = User::find($transaction->user_id);
            $user->decrement('frozen_points', $transaction->amount);

            // 更新Redis缓存
            Redis::setex(
                "transaction:{$transactionId}",
                3600,
                json_encode($transaction->fresh()->toArray())
            );

            Log::info('积分扣取成功', [
                'user_id' => $transaction->user_id,
                'transaction_id' => $transactionId,
                'amount' => $transaction->amount
            ]);

            return ['success' => true, 'message' => '处理成功'];
        });
    }

    /**
     * 处理失败返还
     * 
     * @param int $transactionId 交易ID
     * @param array $error 错误信息
     * @return array
     */
    public function handleFailure($transactionId, $error)
    {
        return DB::transaction(function() use ($transactionId, $error) {
            $transaction = PointsTransaction::find($transactionId);
            if (!$transaction || $transaction->status !== 'frozen') {
                return ['success' => false, 'message' => '交易状态异常'];
            }

            // 更新交易状态
            $transaction->update([
                'status' => 'failed',
                'failure_reason' => $error['message'] ?? '处理失败',
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $user = User::find($transaction->user_id);
            $user->increment('points', $transaction->amount);
            $user->decrement('frozen_points', $transaction->amount);

            // 更新Redis缓存
            Redis::setex(
                "transaction:{$transactionId}",
                3600,
                json_encode($transaction->fresh()->toArray())
            );

            Log::info('积分返还成功', [
                'user_id' => $transaction->user_id,
                'transaction_id' => $transactionId,
                'amount' => $transaction->amount,
                'reason' => $error['message'] ?? '处理失败'
            ]);

            return ['success' => true, 'message' => '积分已返还'];
        });
    }

    /**
     * 验证积分余额
     * 
     * @param int $userId 用户ID
     * @param float $amount 所需积分
     * @return array
     */
    public function validateBalance($userId, $amount)
    {
        $user = User::find($userId);
        if (!$user) {
            return ['valid' => false, 'message' => '用户不存在'];
        }

        if ($user->points < $amount) {
            return [
                'valid' => false,
                'message' => '积分不足',
                'current_balance' => $user->points,
                'required' => $amount,
                'shortage' => $amount - $user->points
            ];
        }

        return [
            'valid' => true,
            'current_balance' => $user->points,
            'after_deduction' => $user->points - $amount
        ];
    }

    /**
     * 根据业务类型获取超时时间
     * 
     * @param string $type 业务类型
     * @return int 超时秒数
     */
    private function getTimeoutByType($type)
    {
        $timeouts = [
            'text_to_image' => 300,    // 5分钟
            'image_to_video' => 1800,  // 30分钟
            'text_generation' => 60,   // 1分钟
            'voice_synthesis' => 120,  // 2分钟
        ];

        return $timeouts[$type] ?? 300;
    }

    /**
     * 获取用户积分余额
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getBalance($userId)
    {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'message' => '用户不存在'];
        }

        return [
            'success' => true,
            'data' => [
                'available_points' => $user->points,
                'frozen_points' => $user->frozen_points,
                'total_points' => $user->points + $user->frozen_points,
                'is_vip' => $user->is_vip,
                'vip_expires_at' => $user->vip_expires_at
            ]
        ];
    }

    /**
     * 获取积分交易记录
     * 
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getTransactions($userId, $page = 1, $limit = 20)
    {
        $transactions = PointsTransaction::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);

        return [
            'success' => true,
            'data' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'total_pages' => $transactions->lastPage(),
                'total_count' => $transactions->total(),
                'per_page' => $transactions->perPage()
            ]
        ];
    }
}
