<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\RecommendationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 个性化推荐与智能算法
 */
class RecommendationController extends Controller
{
    protected $recommendationService;

    public function __construct(RecommendationService $recommendationService)
    {
        $this->recommendationService = $recommendationService;
    }

    /**
     * @ApiTitle(获取内容推荐)
     * @ApiSummary(获取个性化的内容推荐)
     * @ApiMethod(GET)
     * @ApiRoute(/api/recommendations/content)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="推荐类型：publications/templates/users/topics")
     * @ApiParams(name="category", type="string", required=false, description="内容分类过滤")
     * @ApiParams(name="limit", type="int", required=false, description="返回数量")
     * @ApiParams(name="refresh", type="boolean", required=false, description="是否刷新推荐")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendation_type": "publications",
     *     "algorithm": "collaborative_filtering",
     *     "recommendations": [
     *       {
     *         "item_id": 123,
     *         "item_type": "publication",
     *         "title": "精彩的AI故事",
     *         "description": "一个关于未来的故事",
     *         "category": "story",
     *         "author": {
     *           "user_id": 456,
     *           "username": "创作者123",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "score": 0.95,
     *         "reason": "基于你对科幻类故事的喜好",
     *         "tags": ["AI", "科幻", "未来"],
     *         "statistics": {
     *           "view_count": 1250,
     *           "like_count": 89,
     *           "rating": 4.8
     *         },
     *         "recommended_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "personalization_factors": [
     *       {"factor": "兴趣偏好", "weight": 0.4},
     *       {"factor": "历史行为", "weight": 0.3},
     *       {"factor": "社交关系", "weight": 0.2},
     *       {"factor": "热门趋势", "weight": 0.1}
     *     ],
     *     "diversity_score": 0.75,
     *     "freshness_score": 0.85,
     *     "generated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function content(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:publications,templates,users,topics',
                'category' => 'sometimes|string|in:story,image,video,music,mixed',
                'limit' => 'sometimes|integer|min:1|max:50',
                'refresh' => 'sometimes|boolean'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $params = [
                'type' => $request->get('type', 'publications'),
                'category' => $request->get('category'),
                'limit' => $request->get('limit', 20),
                'refresh' => $request->get('refresh', false)
            ];

            $result = $this->recommendationService->getContentRecommendations($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取内容推荐失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取内容推荐失败', []);
        }
    }

    /**
     * @ApiTitle(获取用户推荐)
     * @ApiSummary(推荐可能感兴趣的用户)
     * @ApiMethod(GET)
     * @ApiRoute(/api/recommendations/users)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="推荐类型：follow/collaborate/similar")
     * @ApiParams(name="limit", type="int", required=false, description="返回数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendation_type": "follow",
     *     "users": [
     *       {
     *         "user_id": 456,
     *         "username": "创作达人",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "bio": "专注AI创作的设计师",
     *         "level": 20,
     *         "follower_count": 1250,
     *         "following_count": 340,
     *         "creation_count": 89,
     *         "score": 0.92,
     *         "reason": "你们有相似的创作兴趣",
     *         "common_interests": ["AI创作", "科幻故事", "数字艺术"],
     *         "mutual_followers": 5,
     *         "recent_works": [
     *           {
     *             "work_id": 789,
     *             "title": "未来城市",
     *             "type": "image",
     *             "thumbnail": "https://example.com/thumb.jpg"
     *           }
     *         ],
     *         "is_following": false,
     *         "is_followed_by": false
     *       }
     *     ],
     *     "recommendation_reasons": [
     *       "相似的创作风格",
     *       "共同的兴趣标签",
     *       "互动历史",
     *       "社交网络关系"
     *     ],
     *     "total_available": 156,
     *     "generated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function users(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:follow,collaborate,similar',
                'limit' => 'sometimes|integer|min:1|max:30'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $params = [
                'type' => $request->get('type', 'follow'),
                'limit' => $request->get('limit', 15)
            ];

            $result = $this->recommendationService->getUserRecommendations($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取用户推荐失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户推荐失败', []);
        }
    }

    /**
     * @ApiTitle(获取话题推荐)
     * @ApiSummary(推荐热门话题和标签)
     * @ApiMethod(GET)
     * @ApiRoute(/api/recommendations/topics)
     * @ApiParams(name="type", type="string", required=false, description="话题类型：trending/personalized/new")
     * @ApiParams(name="category", type="string", required=false, description="分类过滤")
     * @ApiParams(name="limit", type="int", required=false, description="返回数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendation_type": "trending",
     *     "topics": [
     *       {
     *         "topic_id": 123,
     *         "name": "AI艺术创作",
     *         "description": "探索人工智能在艺术创作中的应用",
     *         "category": "technology",
     *         "tags": ["AI", "艺术", "创作", "技术"],
     *         "popularity_score": 0.95,
     *         "trend_direction": "rising",
     *         "participant_count": 1250,
     *         "content_count": 456,
     *         "growth_rate": 25.5,
     *         "related_topics": ["数字艺术", "机器学习", "创意设计"],
     *         "recent_highlights": [
     *           {
     *             "content_id": 789,
     *             "title": "AI生成的梦幻画作",
     *             "author": "艺术家小明",
     *             "likes": 234
     *           }
     *         ],
     *         "is_following": false
     *       }
     *     ],
     *     "trending_keywords": [
     *       {"keyword": "AI创作", "mentions": 1250, "growth": 45.2},
     *       {"keyword": "数字艺术", "mentions": 890, "growth": 32.1}
     *     ],
     *     "personalized_score": 0.85,
     *     "updated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function topics(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:trending,personalized,new',
                'category' => 'sometimes|string',
                'limit' => 'sometimes|integer|min:1|max:30'
            ];

            $this->validateData($request->all(), $rules);

            $userId = auth()->id(); // 可能为null（未登录用户）

            $params = [
                'type' => $request->get('type', 'trending'),
                'category' => $request->get('category'),
                'limit' => $request->get('limit', 20)
            ];

            $result = $this->recommendationService->getTopicRecommendations($userId, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取话题推荐失败', [
                'method' => __METHOD__,
                'user_id' => $userId ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取话题推荐失败', []);
        }
    }

    /**
     * @ApiTitle(反馈推荐)
     * @ApiSummary(对推荐结果进行反馈)
     * @ApiMethod(POST)
     * @ApiRoute(/api/recommendations/feedback)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="recommendation_id", type="string", required=true, description="推荐ID")
     * @ApiParams(name="item_id", type="int", required=true, description="推荐项目ID")
     * @ApiParams(name="item_type", type="string", required=true, description="项目类型")
     * @ApiParams(name="feedback_type", type="string", required=true, description="反馈类型：like/dislike/not_interested/irrelevant")
     * @ApiParams(name="reason", type="string", required=false, description="反馈原因")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "反馈提交成功",
     *   "data": {
     *     "recommendation_id": "rec_123456",
     *     "item_id": 789,
     *     "feedback_type": "like",
     *     "processed": true,
     *     "impact": "将增加类似内容的推荐权重",
     *     "submitted_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function feedback(Request $request)
    {
        try {
            $rules = [
                'recommendation_id' => 'required|string',
                'item_id' => 'required|integer',
                'item_type' => 'required|string|in:publication,template,user,topic',
                'feedback_type' => 'required|string|in:like,dislike,not_interested,irrelevant,clicked,shared',
                'reason' => 'sometimes|string|max:500'
            ];

            $messages = [
                'recommendation_id.required' => '推荐ID不能为空',
                'item_id.required' => '项目ID不能为空',
                'item_type.required' => '项目类型不能为空',
                'item_type.in' => '项目类型必须是：publication、template、user、topic之一',
                'feedback_type.required' => '反馈类型不能为空',
                'feedback_type.in' => '反馈类型必须是：like、dislike、not_interested、irrelevant、clicked、shared之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $feedbackData = [
                'recommendation_id' => $request->recommendation_id,
                'item_id' => $request->item_id,
                'item_type' => $request->item_type,
                'feedback_type' => $request->feedback_type,
                'reason' => $request->get('reason')
            ];

            $result = $this->recommendationService->submitFeedback($user->id, $feedbackData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('提交推荐反馈失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '提交推荐反馈失败', []);
        }
    }

    /**
     * @ApiTitle(获取推荐设置)
     * @ApiSummary(获取用户的推荐偏好设置)
     * @ApiMethod(GET)
     * @ApiRoute(/api/recommendations/preferences)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "preferences": {
     *       "content_types": {
     *         "story": true,
     *         "image": true,
     *         "video": false,
     *         "music": true,
     *         "mixed": false
     *       },
     *       "recommendation_frequency": "daily",
     *       "diversity_level": "medium",
     *       "include_trending": true,
     *       "include_new_creators": true,
     *       "language_preference": "zh-CN",
     *       "mature_content": false,
     *       "personalization_level": "high"
     *     },
     *     "interests": [
     *       {"tag": "AI创作", "weight": 0.9},
     *       {"tag": "科幻", "weight": 0.8},
     *       {"tag": "数字艺术", "weight": 0.7}
     *     ],
     *     "blocked_tags": ["暴力", "恐怖"],
     *     "blocked_users": [456, 789],
     *     "algorithm_settings": {
     *       "collaborative_filtering": 0.4,
     *       "content_based": 0.3,
     *       "popularity_based": 0.2,
     *       "social_signals": 0.1
     *     },
     *     "last_updated": "2024-01-01 10:00:00"
     *   }
     * })
     */
    public function preferences(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->recommendationService->getUserPreferences($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取推荐设置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取推荐设置失败', []);
        }
    }

    /**
     * @ApiTitle(更新推荐设置)
     * @ApiSummary(更新用户的推荐偏好设置)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/recommendations/preferences)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="preferences", type="object", required=true, description="偏好设置")
     * @ApiParams(name="interests", type="array", required=false, description="兴趣标签")
     * @ApiParams(name="blocked_tags", type="array", required=false, description="屏蔽标签")
     * @ApiParams(name="blocked_users", type="array", required=false, description="屏蔽用户")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "推荐设置更新成功",
     *   "data": {
     *     "updated_preferences": {
     *       "content_types": {
     *         "story": true,
     *         "image": true,
     *         "video": true,
     *         "music": false,
     *         "mixed": false
     *       },
     *       "recommendation_frequency": "twice_daily",
     *       "diversity_level": "high"
     *     },
     *     "changes_applied": 5,
     *     "recommendation_refresh": true,
     *     "updated_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function updatePreferences(Request $request)
    {
        try {
            $rules = [
                'preferences' => 'required|array',
                'preferences.content_types' => 'sometimes|array',
                'preferences.recommendation_frequency' => 'sometimes|string|in:daily,twice_daily,weekly',
                'preferences.diversity_level' => 'sometimes|string|in:low,medium,high',
                'preferences.include_trending' => 'sometimes|boolean',
                'preferences.include_new_creators' => 'sometimes|boolean',
                'preferences.personalization_level' => 'sometimes|string|in:low,medium,high',
                'interests' => 'sometimes|array',
                'interests.*.tag' => 'required|string',
                'interests.*.weight' => 'required|numeric|min:0|max:1',
                'blocked_tags' => 'sometimes|array',
                'blocked_tags.*' => 'string',
                'blocked_users' => 'sometimes|array',
                'blocked_users.*' => 'integer'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $updateData = [
                'preferences' => $request->preferences,
                'interests' => $request->get('interests', []),
                'blocked_tags' => $request->get('blocked_tags', []),
                'blocked_users' => $request->get('blocked_users', [])
            ];

            $result = $this->recommendationService->updateUserPreferences($user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新推荐设置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新推荐设置失败', []);
        }
    }

    /**
     * @ApiTitle(获取推荐统计)
     * @ApiSummary(获取推荐系统的统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/recommendations/analytics)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：week/month/year")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "month",
     *     "recommendation_stats": {
     *       "total_recommendations": 450,
     *       "clicked_recommendations": 89,
     *       "click_through_rate": 19.8,
     *       "liked_recommendations": 34,
     *       "disliked_recommendations": 12,
     *       "satisfaction_score": 0.74
     *     },
     *     "content_discovery": {
     *       "new_creators_discovered": 15,
     *       "new_topics_explored": 8,
     *       "content_diversity_score": 0.82
     *     },
     *     "algorithm_performance": {
     *       "collaborative_filtering": {"accuracy": 0.78, "usage": 0.4},
     *       "content_based": {"accuracy": 0.72, "usage": 0.3},
     *       "popularity_based": {"accuracy": 0.65, "usage": 0.2},
     *       "social_signals": {"accuracy": 0.69, "usage": 0.1}
     *     },
     *     "trends": [
     *       {"date": "2024-01-01", "recommendations": 15, "clicks": 3},
     *       {"date": "2024-01-02", "recommendations": 18, "clicks": 4}
     *     ],
     *     "top_categories": [
     *       {"category": "story", "percentage": 45.2},
     *       {"category": "image", "percentage": 32.1}
     *     ]
     *   }
     * })
     */
    public function analytics(Request $request)
    {
        try {
            $rules = [
                'period' => 'sometimes|string|in:week,month,year'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $params = [
                'period' => $request->get('period', 'month')
            ];

            $result = $this->recommendationService->getRecommendationAnalytics($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取推荐统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取推荐统计失败', []);
        }
    }

    /**
     * 获取个性化推荐
     * 修复500错误 - 添加缺失的personalized方法
     *
     * @param Request $request
     * @return array
     */
    public function personalized(Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 获取个性化推荐
            $result = $this->recommendationService->getPersonalizedRecommendations($user->id);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::error('获取个性化推荐失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取个性化推荐失败', []);
        }
    }
}
