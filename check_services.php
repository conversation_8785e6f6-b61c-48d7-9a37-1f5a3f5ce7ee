<?php

/**
 * 批量检查服务文件中的 'services_data' => $services_data 字符串
 */

class ServiceDataChecker
{
    private $servicesPath;
    private $results = [];
    private $summary = [];

    public function __construct($servicesPath = 'php/api/app/Services/Api')
    {
        $this->servicesPath = $servicesPath;
    }

    public function check()
    {
        echo "开始检查服务文件中的 'services_data' => \$services_data 字符串...\n\n";
        
        $serviceFiles = $this->scanServiceFiles();
        echo "发现 " . count($serviceFiles) . " 个服务文件\n\n";

        foreach ($serviceFiles as $file) {
            $this->checkServiceFile($file);
        }

        $this->generateReport();
        $this->generateTaskPlan();
    }

    private function scanServiceFiles()
    {
        $files = [];
        $directory = new DirectoryIterator($this->servicesPath);
        
        foreach ($directory as $fileInfo) {
            if ($fileInfo->isDot()) continue;
            
            if ($fileInfo->isFile() && $fileInfo->getExtension() === 'php') {
                $files[] = $fileInfo->getPathname();
            }
        }
        
        sort($files);
        return $files;
    }

    private function checkServiceFile($filePath)
    {
        $fileName = basename($filePath);
        echo "检查文件: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "  ❌ 无法读取文件\n";
            return;
        }

        $publicMethods = $this->extractPublicMethods($content);
        
        $missingMethods = [];
        $totalMethods = count($publicMethods);
        $validMethods = 0;

        foreach ($publicMethods as $method) {
            if ($this->hasServicesDataString($content, $method)) {
                $validMethods++;
            } else {
                if ($method !== '__construct') {
                    $missingMethods[] = $method;
                }
            }
        }

        $this->results[$fileName] = [
            'total_methods' => $totalMethods,
            'valid_methods' => $validMethods,
            'missing_methods' => $missingMethods,
            'missing_count' => count($missingMethods)
        ];

        if (empty($missingMethods)) {
            echo "  ✅ {$validMethods}/{$totalMethods} 方法正确\n";
        } else {
            echo "  ❌ {$validMethods}/{$totalMethods} 方法正确，缺失 " . count($missingMethods) . " 个方法\n";
            foreach ($missingMethods as $method) {
                echo "    - {$method}()\n";
            }
        }
        echo "\n";
    }

    private function extractPublicMethods($content)
    {
        $methods = [];
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
        
        if (!empty($matches[1])) {
            $methods = $matches[1];
        }
        
        return array_unique($methods);
    }

    private function hasServicesDataString($content, $methodName)
    {
        $pattern = '/public\s+function\s+' . preg_quote($methodName, '/') . '\s*\(/';
        preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
        
        if (empty($matches)) {
            return false;
        }
        
        $methodStart = $matches[0][1];
        $nextMethodPattern = '/public\s+function\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(/';
        preg_match($nextMethodPattern, $content, $nextMatches, PREG_OFFSET_CAPTURE, $methodStart + 1);
        
        $methodEnd = !empty($nextMatches) ? $nextMatches[0][1] : strlen($content);
        $methodContent = substr($content, $methodStart, $methodEnd - $methodStart);
        
        return strpos($methodContent, "'services_data' => \$services_data") !== false;
    }

    private function generateReport()
    {
        echo "==================== 检查报告 ====================\n\n";
        
        $totalFiles = count($this->results);
        $correctFiles = 0;
        $totalMethods = 0;
        $totalMissingMethods = 0;
        $problemFiles = [];

        foreach ($this->results as $fileName => $data) {
            $totalMethods += $data['total_methods'];
            $totalMissingMethods += $data['missing_count'];
            
            if ($data['missing_count'] === 0) {
                $correctFiles++;
            } else {
                $problemFiles[] = [
                    'file' => $fileName,
                    'missing_count' => $data['missing_count'],
                    'total_methods' => $data['total_methods'],
                    'missing_rate' => round(($data['missing_count'] / $data['total_methods']) * 100, 1)
                ];
            }
        }

        usort($problemFiles, function($a, $b) {
            return $b['missing_count'] - $a['missing_count'];
        });

        echo "📊 总体统计：\n";
        echo "- 总文件数：{$totalFiles}\n";
        echo "- 正确文件数：{$correctFiles}\n";
        echo "- 问题文件数：" . count($problemFiles) . "\n";
        echo "- 总方法数：{$totalMethods}\n";
        echo "- 缺失方法数：{$totalMissingMethods}\n";
        echo "- 完成率：" . round((($totalMethods - $totalMissingMethods) / $totalMethods) * 100, 1) . "%\n\n";

        if (!empty($problemFiles)) {
            echo "🚨 问题文件列表（按缺失数量排序）：\n";
            foreach ($problemFiles as $file) {
                echo "- {$file['file']}: 缺失 {$file['missing_count']}/{$file['total_methods']} 方法 ({$file['missing_rate']}%)\n";
            }
            echo "\n";
        }

        $this->summary = [
            'total_files' => $totalFiles,
            'correct_files' => $correctFiles,
            'problem_files' => $problemFiles,
            'total_methods' => $totalMethods,
            'total_missing_methods' => $totalMissingMethods
        ];
    }

    private function generateTaskPlan()
    {
        echo "==================== 下一步任务方案 ====================\n\n";
        
        if ($this->summary['total_missing_methods'] === 0) {
            echo "🎉 所有服务文件都已正确实现标准化错误处理！\n";
            return;
        }

        echo "📋 任务优先级方案：\n\n";
        
        $highPriority = array_filter($this->summary['problem_files'], function($file) {
            return $file['missing_count'] >= 10;
        });
        
        $mediumPriority = array_filter($this->summary['problem_files'], function($file) {
            return $file['missing_count'] >= 5 && $file['missing_count'] < 10;
        });
        
        $lowPriority = array_filter($this->summary['problem_files'], function($file) {
            return $file['missing_count'] < 5;
        });

        if (!empty($highPriority)) {
            echo "🔴 高优先级（缺失 ≥ 10个方法）：\n";
            foreach ($highPriority as $file) {
                echo "   - {$file['file']} (缺失 {$file['missing_count']} 个方法)\n";
                $this->printMissingMethods($file['file']);
            }
            echo "\n";
        }

        if (!empty($mediumPriority)) {
            echo "🟡 中优先级（缺失 5-9个方法）：\n";
            foreach ($mediumPriority as $file) {
                echo "   - {$file['file']} (缺失 {$file['missing_count']} 个方法)\n";
                $this->printMissingMethods($file['file']);
            }
            echo "\n";
        }

        if (!empty($lowPriority)) {
            echo "🟢 低优先级（缺失 1-4个方法）：\n";
            foreach ($lowPriority as $file) {
                echo "   - {$file['file']} (缺失 {$file['missing_count']} 个方法)\n";
                $this->printMissingMethods($file['file']);
            }
            echo "\n";
        }

        echo "🛠️ 建议执行顺序：\n";
        echo "1. 优先修复高优先级文件（影响最大）\n";
        echo "2. 然后处理中优先级文件\n";
        echo "3. 最后完成低优先级文件\n\n";
        
        echo "📝 标准化错误处理模板：\n";
        echo "```php\n";
        echo "} catch (\\Exception \$e) {\n";
        echo "    \$services_data = [\n";
        echo "        // 关键的非敏感字段\n";
        echo "    ];\n\n";
        echo "    Log::error('具体业务名称失败', [\n";
        echo "        'method' => __METHOD__,\n";
        echo "        'services_data' => \$services_data,\n";
        echo "        'error' => \$e->getMessage(),\n";
        echo "        'trace' => \$e->getTraceAsString()\n";
        echo "    ]);\n\n";
        echo "    return [\n";
        echo "        'code' => ApiCodeEnum::MY_SERVICE_ERROR,\n";
        echo "        'message' => '具体业务名称失败',\n";
        echo "        'data' => null\n";
        echo "    ];\n";
        echo "}\n";
        echo "```\n";
    }

    private function printMissingMethods($fileName)
    {
        if (isset($this->results[$fileName]['missing_methods'])) {
            foreach ($this->results[$fileName]['missing_methods'] as $method) {
                echo "     * {$method}()\n";
            }
        }
    }
}

try {
    $checker = new ServiceDataChecker();
    $checker->check();
} catch (Exception $e) {
    echo "❌ 检查过程中发生错误: " . $e->getMessage() . "\n";
}
