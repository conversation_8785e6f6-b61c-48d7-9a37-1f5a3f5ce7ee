<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\WebSocketService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * WebSocket连接管理与实时通信（wss）
 */
class WebSocketController extends Controller
{
    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }

    /**
     * @ApiTitle(WebSocket连接认证)
     * @ApiSummary(验证WebSocket连接权限)
     * @ApiMethod(POST)
     * @ApiRoute(/api/websocket/auth)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="client_type", type="string", required=true, description="客户端类型")
     * @ApiParams(name="client_version", type="string", required=false, description="客户端版本")
     * @ApiParams(name="connection_info", type="object", required=false, description="连接信息")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.session_id", type="string", required=true, description="会话ID")
     * @ApiReturnParams (name="data.websocket_url", type="string", required=true, description="WebSocket连接URL")
     * @ApiReturnParams (name="data.allowed_events", type="array", required=true, description="允许的事件类型")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "认证成功",
     *   "data": {
     *     "session_id": "ws_session_123456",
     *     "websocket_url": "wss://api.tiptop.cn:8080",
     *     "allowed_events": ["ai_generation_progress", "ai_generation_completed", "points_changed"]
     *   }
     * })
     */
    public function authenticate(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先执行认证检查
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 参数验证 - 在认证通过后执行（修正：统一参数名称）
            $rules = [
                'client_type' => 'required|string|in:python_tool,web_client',
                'client_info' => 'nullable|array'
            ];

            $messages = [
                'client_type.required' => '客户端类型不能为空',
                'client_type.in' => '客户端类型无效，仅支持python_tool和web_client'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 从认证用户获取user_id（安全修正）
            $userId = $authResult['user']->id;
            $connectionType = $request->client_type;
            $clientInfo = $request->client_info ?? [];

            // 检查连接类型权限（遵循index.mdc架构边界）
            if ($connectionType === 'web_client') {
                // WEB工具禁用WebSocket连接（遵循index.mdc第348行）
                return $this->errorResponse(ApiCodeEnum::FORBIDDEN, 'WEB工具禁用WebSocket连接，请使用HTTP API');
            }

            $user = $authResult['user'];
            $result = $this->webSocketService->authenticateConnection(
                $userId,
                $connectionType,
                $request->ip(),
                $request->header('User-Agent'),
                null, // client_version不再使用
                $clientInfo
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('连接认证失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '连接认证失败');
        }
    }

    /**
     * @ApiTitle(获取WebSocket会话列表)
     * @ApiSummary(获取用户的WebSocket连接会话)
     * @ApiMethod(GET)
     * @ApiRoute(/api/websocket/sessions)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选")
     * @ApiParams(name="client_type", type="string", required=false, description="客户端类型筛选")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="array", required=true, description="会话列表")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": [
     *     {
     *       "session_id": "ws_session_123456",
     *       "client_type": "python_tool",
     *       "status": "connected",
     *       "connected_at": "2024-01-01 12:00:00",
     *       "last_ping_at": "2024-01-01 12:05:00",
     *       "message_count": 10
     *     }
     *   ]
     * })
     */
    public function getSessions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $status = $request->get('status');
            $clientType = $request->get('client_type');

            $result = $this->webSocketService->getUserSessions($user->id, $status, $clientType);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取会话列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取会话列表失败');
        }
    }

    /**
     * @ApiTitle(断开WebSocket连接)
     * @ApiSummary(主动断开指定的WebSocket连接)
     * @ApiMethod(POST)
     * @ApiRoute(/api/websocket/disconnect)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="session_id", type="string", required=true, description="会话ID")
     * @ApiParams(name="reason", type="string", required=false, description="断开原因")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "连接已断开",
     *   "data": {
     *     "session_id": "ws_session_123456",
     *     "disconnected_at": "2024-01-01 12:10:00"
     *   }
     * })
     */
    public function disconnect(Request $request)
    {
        try {

            $rules = [
                'session_id' => 'required|string',
                'reason' => 'sometimes|string|max:500'
            ];

            $messages = [
                'session_id.required' => '会话ID不能为空',
                'reason.max' => '断开原因不能超过500个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->webSocketService->disconnectSession(
                $request->session_id,
                $user->id,
                $request->reason ?? '用户主动断开'
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('断开连接失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '断开连接失败');
        }
    }

    /**
     * @ApiTitle(WebSocket服务状态)
     * @ApiSummary(获取WebSocket服务运行状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/websocket/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="服务状态")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "server_status": "running",
     *     "total_connections": 25,
     *     "active_connections": 20,
     *     "python_tool_connections": 5,
     *     "web_browser_connections": 15,
     *     "server_uptime": "2 hours 30 minutes",
     *     "total_messages_sent": 1500
     *   }
     * })
     */
    public function getStatus(Request $request)
    {
        try {
            $result = $this->webSocketService->getServerStatus();
            
            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取服务器状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取服务器状态失败');
        }
    }


}
