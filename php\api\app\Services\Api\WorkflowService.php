<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

/**
 * 工作流服务类
 * 负责工作流的创建、执行、监控和管理
 */
class WorkflowService extends Service
{
    private $workflowTypes = ['ai_generation', 'data_processing', 'content_creation', 'automation', 'custom'];
    private $stepTypes = ['ai_call', 'data_transform', 'condition', 'loop', 'webhook', 'delay', 'notification'];
    private $statuses = ['draft', 'active', 'paused', 'completed', 'failed', 'cancelled'];
    
    /**
     * 创建工作流
     */
    public function createWorkflow($data)
    {
        try {
            $workflow = [
                'id' => 'wf_' . uniqid(),
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'type' => $data['type'] ?? 'custom',
                'steps' => $data['steps'] ?? [],
                'triggers' => $data['triggers'] ?? [],
                'variables' => $data['variables'] ?? [],
                'settings' => $data['settings'] ?? [],
                'status' => 'draft',
                'created_by' => $data['created_by'] ?? null,
                'created_at' => now(),
                'updated_at' => now()
            ];
            
            // 验证工作流数据
            $this->validateWorkflow($workflow);
            
            // 验证步骤配置
            $this->validateWorkflowSteps($workflow['steps']);
            
            Log::info("工作流创建成功", ['workflow_id' => $workflow['id'], 'name' => $workflow['name']]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流创建成功',
                'data' => $workflow
            ];
        } catch (\Exception $e) {
            $services_data = [
                'name' => $data['name'] ?? null,
                'description' => $data['description'] ?? null,
                'steps_count' => is_array($data['steps'] ?? []) ? count($data['steps']) : 0,
            ];

            Log::error('工作流创建失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流创建失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 执行工作流
     */
    public function executeWorkflow($workflowId, $inputData = [])
    {
        try {
            // 获取工作流配置
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            // 创建执行实例
            $execution = [
                'id' => 'exec_' . uniqid(),
                'workflow_id' => $workflowId,
                'status' => 'running',
                'input_data' => $inputData,
                'current_step' => 0,
                'step_results' => [],
                'variables' => array_merge($workflow['variables'] ?? [], $inputData),
                'started_at' => now(),
                'completed_at' => null,
                'error' => null
            ];
            
            // 异步执行工作流
            $this->executeWorkflowAsync($execution, $workflow);
            
            Log::info("工作流执行开始", [
                'workflow_id' => $workflowId,
                'execution_id' => $execution['id']
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流执行已开始',
                'data' => [
                    'execution_id' => $execution['id'],
                    'workflow_id' => $workflowId,
                    'status' => $execution['status'],
                    'started_at' => $execution['started_at']
                ]
            ];
        } catch (\Exception $e) {
            Log::error("工作流执行失败", ['error' => $e->getMessage(), 'workflow_id' => $workflowId]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流执行失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取工作流执行状态
     */
    public function getExecutionStatus($executionId)
    {
        try {
            // 模拟获取执行状态
            $execution = [
                'id' => $executionId,
                'workflow_id' => 'wf_' . substr($executionId, 5),
                'status' => $this->getRandomStatus(),
                'current_step' => rand(0, 5),
                'total_steps' => 6,
                'progress' => rand(10, 100),
                'step_results' => $this->generateStepResults(),
                'started_at' => now()->subMinutes(rand(5, 60)),
                'completed_at' => rand(0, 1) ? now()->subMinutes(rand(1, 10)) : null,
                'duration' => rand(30, 3600), // 秒
                'error' => null
            ];
            
            if ($execution['status'] === 'failed') {
                $execution['error'] = '步骤3执行失败：API调用超时';
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '执行状态获取成功',
                'data' => $execution
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '执行状态获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取工作流列表
     */
    public function getWorkflows($params = [])
    {
        try {
            // 模拟工作流数据
            $workflows = [
                [
                    'id' => 'wf_001',
                    'name' => 'AI内容生成流程',
                    'description' => '自动生成文章、图片和视频的完整流程',
                    'type' => 'ai_generation',
                    'status' => 'active',
                    'steps_count' => 6,
                    'executions_count' => 45,
                    'success_rate' => 92.5,
                    'created_by' => 'admin',
                    'created_at' => now()->subDays(15),
                    'last_executed' => now()->subHours(2)
                ],
                [
                    'id' => 'wf_002',
                    'name' => '数据处理管道',
                    'description' => '处理用户上传数据并生成报告',
                    'type' => 'data_processing',
                    'status' => 'active',
                    'steps_count' => 4,
                    'executions_count' => 128,
                    'success_rate' => 98.2,
                    'created_by' => 'editor',
                    'created_at' => now()->subDays(30),
                    'last_executed' => now()->subMinutes(30)
                ],
                [
                    'id' => 'wf_003',
                    'name' => '自动化营销流程',
                    'description' => '根据用户行为自动发送个性化营销内容',
                    'type' => 'automation',
                    'status' => 'paused',
                    'steps_count' => 8,
                    'executions_count' => 67,
                    'success_rate' => 85.7,
                    'created_by' => 'marketing',
                    'created_at' => now()->subDays(45),
                    'last_executed' => now()->subDays(3)
                ]
            ];
            
            // 过滤工作流
            $type = $params['type'] ?? null;
            $status = $params['status'] ?? null;
            $createdBy = $params['created_by'] ?? null;
            
            if ($type) {
                $workflows = array_filter($workflows, fn($w) => $w['type'] === $type);
            }
            if ($status) {
                $workflows = array_filter($workflows, fn($w) => $w['status'] === $status);
            }
            if ($createdBy) {
                $workflows = array_filter($workflows, fn($w) => $w['created_by'] === $createdBy);
            }
            
            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;
            $offset = ($page - 1) * $perPage;
            $paginatedWorkflows = array_slice($workflows, $offset, $perPage);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流列表获取成功',
                'data' => [
                    'workflows' => array_values($paginatedWorkflows),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => count($workflows),
                        'total_pages' => ceil(count($workflows) / $perPage)
                    ],
                    'filters' => [
                        'type' => $type,
                        'status' => $status,
                        'created_by' => $createdBy
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流列表获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取工作流详情
     */
    public function getWorkflow($workflowId)
    {
        try {
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流详情获取成功',
                'data' => $workflow
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流详情获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 更新工作流
     */
    public function updateWorkflow($workflowId, $data)
    {
        try {
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            // 更新工作流数据
            $updatedWorkflow = array_merge($workflow, [
                'name' => $data['name'] ?? $workflow['name'],
                'description' => $data['description'] ?? $workflow['description'],
                'steps' => $data['steps'] ?? $workflow['steps'],
                'triggers' => $data['triggers'] ?? $workflow['triggers'],
                'variables' => $data['variables'] ?? $workflow['variables'],
                'settings' => $data['settings'] ?? $workflow['settings'],
                'updated_at' => now()
            ]);
            
            // 验证更新后的工作流
            $this->validateWorkflow($updatedWorkflow);
            $this->validateWorkflowSteps($updatedWorkflow['steps']);
            
            Log::info("工作流更新成功", ['workflow_id' => $workflowId]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流更新成功',
                'data' => $updatedWorkflow
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流更新失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 删除工作流
     */
    public function deleteWorkflow($workflowId)
    {
        try {
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            Log::info("工作流删除成功", ['workflow_id' => $workflowId]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流删除成功',
                'data' => [
                    'workflow_id' => $workflowId,
                    'deleted_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流删除失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 暂停工作流
     */
    public function pauseWorkflow($workflowId)
    {
        try {
            Log::info("工作流暂停", ['workflow_id' => $workflowId]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流已暂停',
                'data' => [
                    'workflow_id' => $workflowId,
                    'status' => 'paused',
                    'paused_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流暂停失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 恢复工作流
     */
    public function resumeWorkflow($workflowId)
    {
        try {
            Log::info("工作流恢复", ['workflow_id' => $workflowId]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流已恢复',
                'data' => [
                    'workflow_id' => $workflowId,
                    'status' => 'active',
                    'resumed_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流恢复失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取工作流执行历史
     */
    public function getExecutionHistory($workflowId, $params = [])
    {
        try {
            // 模拟执行历史数据
            $executions = [];
            for ($i = 0; $i < 20; $i++) {
                $status = $this->getRandomStatus();
                $startTime = now()->subDays(rand(0, 30))->subHours(rand(0, 23));
                $duration = rand(30, 3600);
                
                $executions[] = [
                    'id' => 'exec_' . str_pad($i + 1, 3, '0', STR_PAD_LEFT),
                    'workflow_id' => $workflowId,
                    'status' => $status,
                    'progress' => $status === 'completed' ? 100 : ($status === 'running' ? rand(10, 90) : rand(0, 100)),
                    'started_at' => $startTime,
                    'completed_at' => in_array($status, ['completed', 'failed', 'cancelled']) ? $startTime->copy()->addSeconds($duration) : null,
                    'duration' => in_array($status, ['completed', 'failed', 'cancelled']) ? $duration : null,
                    'triggered_by' => rand(0, 1) ? 'user_' . rand(1, 100) : 'system',
                    'error' => $status === 'failed' ? '步骤执行超时' : null
                ];
            }
            
            // 按时间倒序排列
            usort($executions, fn($a, $b) => $b['started_at']->timestamp - $a['started_at']->timestamp);
            
            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 10;
            $offset = ($page - 1) * $perPage;
            $paginatedExecutions = array_slice($executions, $offset, $perPage);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '执行历史获取成功',
                'data' => [
                    'executions' => $paginatedExecutions,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => count($executions),
                        'total_pages' => ceil(count($executions) / $perPage)
                    ],
                    'summary' => [
                        'total_executions' => count($executions),
                        'success_rate' => round(count(array_filter($executions, fn($e) => $e['status'] === 'completed')) / count($executions) * 100, 1),
                        'average_duration' => round(array_sum(array_filter(array_column($executions, 'duration'))) / count(array_filter($executions, fn($e) => $e['duration'])), 0)
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '执行历史获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取工作流统计
     */
    public function getWorkflowStats($params = [])
    {
        try {
            $timeRange = $params['time_range'] ?? '7d';
            
            $stats = [
                'summary' => [
                    'total_workflows' => rand(50, 200),
                    'active_workflows' => rand(30, 150),
                    'total_executions' => rand(1000, 5000),
                    'success_rate' => rand(85, 98) + rand(0, 99) / 100,
                    'average_execution_time' => rand(60, 300) // 秒
                ],
                'by_type' => [
                    'ai_generation' => rand(10, 50),
                    'data_processing' => rand(15, 60),
                    'content_creation' => rand(8, 30),
                    'automation' => rand(12, 40),
                    'custom' => rand(5, 20)
                ],
                'by_status' => [
                    'active' => rand(30, 100),
                    'paused' => rand(5, 20),
                    'draft' => rand(10, 30),
                    'completed' => rand(50, 200),
                    'failed' => rand(2, 10)
                ],
                'execution_trends' => $this->generateExecutionTrends($timeRange),
                'top_workflows' => [
                    [
                        'id' => 'wf_001',
                        'name' => 'AI内容生成流程',
                        'executions' => rand(100, 500),
                        'success_rate' => rand(90, 99)
                    ],
                    [
                        'id' => 'wf_002',
                        'name' => '数据处理管道',
                        'executions' => rand(80, 400),
                        'success_rate' => rand(85, 98)
                    ],
                    [
                        'id' => 'wf_003',
                        'name' => '自动化营销流程',
                        'executions' => rand(60, 300),
                        'success_rate' => rand(80, 95)
                    ]
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流统计获取成功',
                'data' => [
                    'time_range' => $timeRange,
                    'generated_at' => now(),
                    'stats' => $stats
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流统计获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    // 私有方法实现
    
    private function getWorkflowById($workflowId)
    {
        // 模拟获取工作流详情
        $workflows = [
            'wf_001' => [
                'id' => 'wf_001',
                'name' => 'AI内容生成流程',
                'description' => '自动生成文章、图片和视频的完整流程',
                'type' => 'ai_generation',
                'status' => 'active',
                'steps' => [
                    [
                        'id' => 'step_1',
                        'name' => '文本生成',
                        'type' => 'ai_call',
                        'config' => ['model' => 'deepseek-chat', 'max_tokens' => 1000]
                    ],
                    [
                        'id' => 'step_2',
                        'name' => '图片生成',
                        'type' => 'ai_call',
                        'config' => ['model' => 'liblib-image', 'size' => '1024x1024']
                    ],
                    [
                        'id' => 'step_3',
                        'name' => '内容整合',
                        'type' => 'data_transform',
                        'config' => ['template' => 'article_template']
                    ]
                ],
                'triggers' => [
                    ['type' => 'manual'],
                    ['type' => 'schedule', 'cron' => '0 9 * * *']
                ],
                'variables' => [
                    'topic' => '',
                    'style' => 'professional',
                    'language' => 'zh-CN'
                ],
                'settings' => [
                    'timeout' => 3600,
                    'retry_attempts' => 3,
                    'notification_on_completion' => true
                ],
                'created_by' => 'admin',
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(2)
            ]
        ];
        
        return $workflows[$workflowId] ?? null;
    }
    
    private function validateWorkflow($workflow)
    {
        if (empty($workflow['name'])) {
            throw new \InvalidArgumentException("工作流名称不能为空");
        }
        
        if (!in_array($workflow['type'], $this->workflowTypes)) {
            throw new \InvalidArgumentException("无效的工作流类型: {$workflow['type']}");
        }
        
        if (empty($workflow['steps'])) {
            throw new \InvalidArgumentException("工作流必须包含至少一个步骤");
        }
    }
    
    private function validateWorkflowSteps($steps)
    {
        foreach ($steps as $index => $step) {
            if (empty($step['id'])) {
                throw new \InvalidArgumentException("步骤 {$index} 缺少ID");
            }
            
            if (empty($step['name'])) {
                throw new \InvalidArgumentException("步骤 {$index} 缺少名称");
            }
            
            if (!in_array($step['type'], $this->stepTypes)) {
                throw new \InvalidArgumentException("步骤 {$index} 类型无效: {$step['type']}");
            }
        }
    }
    
    private function executeWorkflowAsync($execution, $workflow)
    {
        // 模拟异步执行工作流
        // 实际项目中应该使用队列系统
        Log::info("开始异步执行工作流", [
            'execution_id' => $execution['id'],
            'workflow_id' => $workflow['id'],
            'steps_count' => count($workflow['steps'])
        ]);
    }
    
    private function getRandomStatus()
    {
        $statuses = ['running', 'completed', 'failed', 'cancelled'];
        $weights = [20, 60, 15, 5]; // 权重
        
        $rand = rand(1, 100);
        $cumulative = 0;
        
        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $statuses[$index];
            }
        }
        
        return 'completed';
    }
    
    private function generateStepResults()
    {
        return [
            [
                'step_id' => 'step_1',
                'status' => 'completed',
                'started_at' => now()->subMinutes(10),
                'completed_at' => now()->subMinutes(8),
                'output' => ['text' => '生成的文章内容...']
            ],
            [
                'step_id' => 'step_2',
                'status' => 'completed',
                'started_at' => now()->subMinutes(8),
                'completed_at' => now()->subMinutes(5),
                'output' => ['image_url' => 'https://example.com/generated-image.jpg']
            ],
            [
                'step_id' => 'step_3',
                'status' => 'running',
                'started_at' => now()->subMinutes(5),
                'completed_at' => null,
                'output' => null
            ]
        ];
    }
    
    /**
     * 获取工作流详情（支持WorkflowController调用）
     */
    public function getWorkflowDetail($workflowId, $userId, $isAdmin)
    {
        try {
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            // 权限检查
            if (!$isAdmin && $workflow['created_by'] !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权限访问此工作流',
                    'data' => null
                ];
            }
            
            // 添加统计信息
            $workflow['statistics'] = [
                'total_executions' => rand(50, 500),
                'successful_executions' => rand(45, 450),
                'failed_executions' => rand(2, 50),
                'success_rate' => rand(85, 98) + rand(0, 99) / 100,
                'avg_execution_time' => rand(300, 3600),
                'last_execution' => now()->subHours(rand(1, 24))
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流详情获取成功',
                'data' => $workflow
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流详情获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 执行工作流（支持WorkflowController调用，带详细执行信息）
     */
    public function executeWorkflowWithData($executionData)
    {
        try {
            $workflowId = $executionData['workflow_id'];
            $inputData = $executionData['input_data'] ?? [];
            $priority = $executionData['priority'] ?? 'normal';
            $executedBy = $executionData['executed_by'];
            
            // 获取工作流配置
            $workflow = $this->getWorkflowById($workflowId);
            
            if (!$workflow) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '工作流不存在',
                    'data' => null
                ];
            }
            
            if ($workflow['status'] !== 'active') {
                return [
                    'code' => ApiCodeEnum::BAD_REQUEST,
                    'message' => '工作流未激活，无法执行',
                    'data' => null
                ];
            }
            
            // 创建执行实例
            $executionId = 'exec_' . uniqid();
            $execution = [
                'execution_id' => $executionId,
                'workflow_id' => $workflowId,
                'workflow_name' => $workflow['name'],
                'status' => 'running',
                'current_step' => 1,
                'current_step_name' => $workflow['steps'][0]['name'] ?? '开始步骤',
                'progress' => 0,
                'estimated_time' => rand(600, 3600),
                'started_at' => now(),
                'input_data' => $inputData,
                'priority' => $priority,
                'executed_by' => $executedBy
            ];
            
            // 异步执行工作流
            $this->executeWorkflowAsync($execution, $workflow);
            
            Log::info("工作流执行开始", [
                'workflow_id' => $workflowId,
                'execution_id' => $executionId,
                'executed_by' => $executedBy
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流执行启动成功',
                'data' => $execution
            ];
        } catch (\Exception $e) {
            Log::error("工作流执行失败", ['error' => $e->getMessage(), 'data' => $executionData]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流执行失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取工作流执行状态（支持WorkflowController调用，带权限验证）
     */
    public function getExecutionStatusWithAuth($executionId, $userId, $isAdmin)
    {
        try {
            // 模拟获取执行状态
            $execution = [
                'execution_id' => $executionId,
                'workflow_id' => 1,
                'workflow_name' => 'AI创作完整流程',
                'status' => $this->getRandomStatus(),
                'current_step' => rand(1, 5),
                'current_step_name' => '图像生成',
                'progress' => rand(10, 100),
                'steps_status' => [
                    [
                        'step_id' => 1,
                        'step_name' => '故事创作',
                        'status' => 'completed',
                        'started_at' => now()->subMinutes(10),
                        'completed_at' => now()->subMinutes(8),
                        'output' => [
                            'story_id' => 123,
                            'story_content' => '从前有一只小猫...'
                        ]
                    ],
                    [
                        'step_id' => 2,
                        'step_name' => '角色选择',
                        'status' => 'waiting_input',
                        'started_at' => now()->subMinutes(8),
                        'timeout_at' => now()->addMinutes(52),
                        'required_input' => [
                            'character_id' => 'integer',
                            'character_style' => 'string'
                        ]
                    ]
                ],
                'estimated_remaining_time' => rand(300, 1800),
                'started_at' => now()->subMinutes(10),
                'updated_at' => now()->subMinutes(2)
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '执行状态获取成功',
                'data' => $execution
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '执行状态获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 提供步骤输入
     */
    public function provideStepInput($inputData)
    {
        try {
            $executionId = $inputData['execution_id'];
            $stepId = $inputData['step_id'];
            $stepInputData = $inputData['input_data'];
            $providedBy = $inputData['provided_by'];
            
            Log::info("步骤输入提供", [
                'execution_id' => $executionId,
                'step_id' => $stepId,
                'provided_by' => $providedBy
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '输入数据提交成功，工作流继续执行',
                'data' => [
                    'execution_id' => $executionId,
                    'step_id' => $stepId,
                    'status' => 'processing',
                    'next_step' => $stepId + 1,
                    'updated_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '步骤输入提交失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 取消工作流执行
     */
    public function cancelExecution($cancelData)
    {
        try {
            $executionId = $cancelData['execution_id'];
            $reason = $cancelData['reason'];
            $cancelledBy = $cancelData['cancelled_by'];
            
            Log::info("工作流执行取消", [
                'execution_id' => $executionId,
                'reason' => $reason,
                'cancelled_by' => $cancelledBy
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '工作流执行取消成功',
                'data' => [
                    'execution_id' => $executionId,
                    'status' => 'cancelled',
                    'cancelled_by' => $cancelledBy,
                    'cancelled_at' => now(),
                    'reason' => $reason,
                    'completed_steps' => rand(1, 3),
                    'total_steps' => 5
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '工作流执行取消失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取工作流执行历史（支持WorkflowController调用）
     */
    public function getExecutionHistoryWithFilters($filters)
    {
        try {
            $workflowId = $filters['workflow_id'];
            $status = $filters['status'];
            $startDate = $filters['start_date'];
            $endDate = $filters['end_date'];
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;
            $userId = $filters['user_id'];
            $isAdmin = $filters['is_admin'];
            
            // 模拟执行历史数据
            $executions = [];
            for ($i = 0; $i < 50; $i++) {
                $executionStatus = $this->getRandomStatus();
                $startTime = now()->subDays(rand(0, 30))->subHours(rand(0, 23));
                $duration = rand(300, 3600);
                
                $execution = [
                    'execution_id' => 'exec_' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                    'status' => $executionStatus,
                    'progress' => $executionStatus === 'completed' ? 100 : ($executionStatus === 'running' ? rand(10, 90) : rand(0, 100)),
                    'executed_by' => 'user_' . rand(1, 100),
                    'started_at' => $startTime,
                    'completed_at' => in_array($executionStatus, ['completed', 'failed', 'cancelled']) ? $startTime->copy()->addSeconds($duration) : null,
                    'execution_time' => in_array($executionStatus, ['completed', 'failed', 'cancelled']) ? $duration : null,
                    'steps_completed' => rand(1, 5),
                    'steps_total' => 5,
                    'success' => $executionStatus === 'completed'
                ];
                
                $executions[] = $execution;
            }
            
            // 应用过滤器
            if ($status) {
                $executions = array_filter($executions, function($execution) use ($status) {
                    return $execution['status'] === $status;
                });
            }
            
            // 按时间倒序排列
            usort($executions, function($a, $b) {
                return $b['started_at']->timestamp - $a['started_at']->timestamp;
            });
            
            $total = count($executions);
            $offset = ($page - 1) * $perPage;
            $paginatedExecutions = array_slice($executions, $offset, $perPage);
            $lastPage = ceil($total / $perPage);
            
            // 计算统计信息
            $completedExecutions = array_filter($executions, function($e) {
                return $e['status'] === 'completed';
            });
            $successRate = $total > 0 ? (count($completedExecutions) / $total) * 100 : 0;
            $avgExecutionTime = count($completedExecutions) > 0 ? 
                array_sum(array_column($completedExecutions, 'execution_time')) / count($completedExecutions) : 0;
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '执行历史获取成功',
                'data' => [
                    'workflow_id' => $workflowId,
                    'workflow_name' => 'AI创作完整流程',
                    'executions' => array_values($paginatedExecutions),
                    'statistics' => [
                        'total_executions' => $total,
                        'success_rate' => round($successRate, 2),
                        'avg_execution_time' => round($avgExecutionTime, 0),
                        'fastest_execution' => $total > 0 ? min(array_filter(array_column($executions, 'execution_time'))) : 0,
                        'slowest_execution' => $total > 0 ? max(array_filter(array_column($executions, 'execution_time'))) : 0
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '执行历史获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    private function generateExecutionTrends($timeRange)
    {
        $trends = [];
        $now = now();
        
        switch ($timeRange) {
            case '24h':
                for ($i = 23; $i >= 0; $i--) {
                    $time = $now->copy()->subHours($i);
                    $trends[] = [
                        'time' => $time->format('H:00'),
                        'executions' => rand(5, 50),
                        'success' => rand(4, 45),
                        'failed' => rand(0, 5)
                    ];
                }
                break;
            case '7d':
                for ($i = 6; $i >= 0; $i--) {
                    $time = $now->copy()->subDays($i);
                    $trends[] = [
                        'time' => $time->format('m-d'),
                        'executions' => rand(50, 200),
                        'success' => rand(45, 180),
                        'failed' => rand(2, 20)
                    ];
                }
                break;
            case '30d':
                for ($i = 29; $i >= 0; $i--) {
                    $time = $now->copy()->subDays($i);
                    $trends[] = [
                        'time' => $time->format('m-d'),
                        'executions' => rand(20, 100),
                        'success' => rand(18, 90),
                        'failed' => rand(1, 10)
                    ];
                }
                break;
            default:
                for ($i = 6; $i >= 0; $i--) {
                    $time = $now->copy()->subDays($i);
                    $trends[] = [
                        'time' => $time->format('m-d'),
                        'executions' => rand(50, 200),
                        'success' => rand(45, 180),
                        'failed' => rand(2, 20)
                    ];
                }
        }
        
        return $trends;
    }
}