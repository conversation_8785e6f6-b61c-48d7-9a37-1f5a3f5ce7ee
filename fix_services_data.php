<?php

/**
 * 自动修复服务文件中缺失的 'services_data' => $services_data 字符串
 */

class ServiceDataFixer
{
    private $servicesPath;
    private $results = [];
    private $backupPath;

    public function __construct($servicesPath = 'php/api/app/Services/Api')
    {
        $this->servicesPath = $servicesPath;
        $this->backupPath = $servicesPath . '_backup_' . date('Y-m-d_H-i-s');
    }

    /**
     * 执行修复
     */
    public function fix()
    {
        echo "开始自动修复服务文件中缺失的 'services_data' => \$services_data 字符串...\n\n";
        
        // 1. 创建备份
        $this->createBackup();
        
        // 2. 扫描需要修复的文件
        $serviceFiles = $this->scanServiceFiles();
        echo "发现 " . count($serviceFiles) . " 个服务文件\n\n";

        // 3. 修复每个文件
        foreach ($serviceFiles as $file) {
            $this->fixServiceFile($file);
        }

        // 4. 生成修复报告
        $this->generateReport();
    }

    /**
     * 创建备份
     */
    private function createBackup()
    {
        echo "创建备份到: {$this->backupPath}\n";
        
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
        
        $this->copyDirectory($this->servicesPath, $this->backupPath);
        echo "备份创建完成\n\n";
    }

    /**
     * 复制目录
     */
    private function copyDirectory($src, $dst)
    {
        $dir = opendir($src);
        if (!is_dir($dst)) {
            mkdir($dst, 0755, true);
        }
        
        while (($file = readdir($dir)) !== false) {
            if ($file != '.' && $file != '..') {
                if (is_dir($src . '/' . $file)) {
                    $this->copyDirectory($src . '/' . $file, $dst . '/' . $file);
                } else {
                    copy($src . '/' . $file, $dst . '/' . $file);
                }
            }
        }
        closedir($dir);
    }

    /**
     * 扫描所有服务文件
     */
    private function scanServiceFiles()
    {
        $files = [];
        $directory = new DirectoryIterator($this->servicesPath);
        
        foreach ($directory as $fileInfo) {
            if ($fileInfo->isDot()) continue;
            
            if ($fileInfo->isFile() && $fileInfo->getExtension() === 'php') {
                $files[] = $fileInfo->getPathname();
            }
        }
        
        sort($files);
        return $files;
    }

    /**
     * 修复单个服务文件
     */
    private function fixServiceFile($filePath)
    {
        $fileName = basename($filePath);
        echo "修复文件: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "  ❌ 无法读取文件\n";
            return;
        }

        $publicMethods = $this->extractPublicMethods($content);
        $fixedMethods = 0;
        $totalMethods = count($publicMethods);

        foreach ($publicMethods as $method) {
            if ($method === '__construct') continue; // 跳过构造函数
            
            if (!$this->hasServicesDataString($content, $method)) {
                $content = $this->fixMethodCatchBlock($content, $method);
                $fixedMethods++;
            }
        }

        // 写回文件
        if ($fixedMethods > 0) {
            file_put_contents($filePath, $content);
            echo "  ✅ 修复了 {$fixedMethods}/{$totalMethods} 个方法\n";
        } else {
            echo "  ✅ 无需修复\n";
        }

        $this->results[$fileName] = [
            'total_methods' => $totalMethods,
            'fixed_methods' => $fixedMethods
        ];
        
        echo "\n";
    }

    /**
     * 提取所有 public 方法
     */
    private function extractPublicMethods($content)
    {
        $methods = [];
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
        
        if (!empty($matches[1])) {
            $methods = $matches[1];
        }
        
        return array_unique($methods);
    }

    /**
     * 检查方法是否包含 'services_data' => $services_data 字符串
     */
    private function hasServicesDataString($content, $methodName)
    {
        $pattern = '/public\s+function\s+' . preg_quote($methodName, '/') . '\s*\(/';
        preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
        
        if (empty($matches)) {
            return false;
        }
        
        $methodStart = $matches[0][1];
        $nextMethodPattern = '/public\s+function\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(/';
        preg_match($nextMethodPattern, $content, $nextMatches, PREG_OFFSET_CAPTURE, $methodStart + 1);
        
        $methodEnd = !empty($nextMatches) ? $nextMatches[0][1] : strlen($content);
        $methodContent = substr($content, $methodStart, $methodEnd - $methodStart);
        
        return strpos($methodContent, "'services_data' => \$services_data") !== false;
    }

    /**
     * 修复方法的catch块
     */
    private function fixMethodCatchBlock($content, $methodName)
    {
        // 查找方法的catch块
        $pattern = '/public\s+function\s+' . preg_quote($methodName, '/') . '\s*\([^)]*\)\s*\{.*?\} catch \(\\\Exception \$e\) \{[^}]*\}/s';
        
        $callback = function($matches) use ($methodName) {
            $catchBlock = $matches[0];
            
            // 检查是否已经有标准化的错误处理
            if (strpos($catchBlock, "'services_data' => \$services_data") !== false) {
                return $catchBlock; // 已经有了，不需要修复
            }
            
            // 提取现有的catch块内容
            preg_match('/\} catch \(\\\Exception \$e\) \{(.*?)\}/s', $catchBlock, $catchMatches);
            if (empty($catchMatches[1])) {
                return $catchBlock; // 无法解析，跳过
            }
            
            $existingCatchContent = trim($catchMatches[1]);
            
            // 生成新的catch块
            $newCatchBlock = $this->generateStandardCatchBlock($methodName, $existingCatchContent);
            
            // 替换catch块
            return str_replace($catchMatches[0], $newCatchBlock, $catchBlock);
        };
        
        return preg_replace_callback($pattern, $callback, $content);
    }

    /**
     * 生成标准化的catch块
     */
    private function generateStandardCatchBlock($methodName, $existingContent)
    {
        // 提取现有的返回信息
        $message = '操作失败';
        $code = 'ApiCodeEnum::MY_SERVICE_ERROR';
        
        // 尝试从现有内容中提取消息
        if (preg_match("/'message'\s*=>\s*'([^']+)'/", $existingContent, $matches)) {
            $message = $matches[1];
            $message = str_replace('：' . '$e->getMessage()', '', $message);
            $message = str_replace(': ' . '$e->getMessage()', '', $message);
        }
        
        // 尝试从现有内容中提取错误码
        if (preg_match('/ApiCodeEnum::([A-Z_]+)/', $existingContent, $matches)) {
            $code = 'ApiCodeEnum::' . $matches[1];
        }

        return "} catch (\\Exception \$e) {
            \$services_data = [
                // 关键的非敏感字段
            ];

            Log::error('{$message}', [
                'method' => __METHOD__,
                'services_data' => \$services_data,
                'error' => \$e->getMessage(),
                'trace' => \$e->getTraceAsString()
            ]);

            return [
                'code' => {$code},
                'message' => '{$message}',
                'data' => null
            ];
        }";
    }

    /**
     * 生成修复报告
     */
    private function generateReport()
    {
        echo "==================== 修复报告 ====================\n\n";
        
        $totalFiles = count($this->results);
        $fixedFiles = 0;
        $totalFixedMethods = 0;

        foreach ($this->results as $fileName => $data) {
            $totalFixedMethods += $data['fixed_methods'];
            
            if ($data['fixed_methods'] > 0) {
                $fixedFiles++;
            }
        }

        echo "📊 修复统计：\n";
        echo "- 总文件数：{$totalFiles}\n";
        echo "- 修复文件数：{$fixedFiles}\n";
        echo "- 总修复方法数：{$totalFixedMethods}\n\n";

        if ($fixedFiles > 0) {
            echo "🔧 修复详情：\n";
            foreach ($this->results as $fileName => $data) {
                if ($data['fixed_methods'] > 0) {
                    echo "- {$fileName}: 修复了 {$data['fixed_methods']} 个方法\n";
                }
            }
            echo "\n";
        }

        echo "💾 备份位置：{$this->backupPath}\n";
        echo "✅ 修复完成！\n";
    }
}

// 执行修复
try {
    $fixer = new ServiceDataFixer();
    $fixer->fix();
} catch (Exception $e) {
    echo "❌ 修复过程中发生错误: " . $e->getMessage() . "\n";
}
