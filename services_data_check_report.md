# Services/Api 目录 'services_data' 检查报告

## 检查概要

- **总服务文件数量**: 51
- **总public方法数量**: 353
- **缺少'services_data'的方法数量**: 306

## ❌ 缺少'services_data' => $services_data, 的方法

### AiGenerationService.php

- generateText
- getTaskStatus
- getUserTasks
- retryTask

### AiLoadBalancingService.php

- distributeTasks

### AiModelService.php

- switchUserModel
- getPerformanceMetrics
- checkServiceHealth
- getUserDefaultModel
- getModelByPlatform

### AiPlatformFallbackService.php

- executeFallback
- shouldFallback

### AiPlatformHealthService.php

- checkPlatformHealth
- checkAllPlatformsHealth
- getPlatformAvailability
- recordPerformanceMetric
- getHealthSummary
- checkAlertConditions
- getPlatformComparison
- getBusinessPlatforms

### AiPlatformSelectionService.php

- selectOptimalPlatform
- validateBusinessType
- getSupportedPlatforms
- isPlatformSupported

### AiTaskService.php

- cancelTask
- getBatchTaskStatus
- getTaskRecoveryStatus
- getTimeoutConfig

### AudioService.php

- mixAudio
- getAudioMixStatus
- enhanceAudio
- getAudioEnhanceStatus

### AuthService.php

- logout
- forgotPassword
- resetPassword
- refreshToken

### BatchService.php

- startBatch
- cancelBatch
- getBatchResults
- getBatchList
- retryFailedItems
- getBatchStats
- generateResources
- getResourcesStatus

### CacheService.php

- delete
- setMultiple
- getMultiple
- flush
- healthCheck
- setWithTags
- flushTags
- clearCache
- warmupCache
- getKeys
- getValue
- setValue
- deleteKeys
- getConfig
- getStats

### CharacterService.php

- getCategories
- bindCharacter
- unbindCharacter
- updateBinding
- getUserBindings
- getCharacters
- getCharacterDetail
- getRecommendations
- generateCharacter

### ConfigService.php

- set
- getAll
- getAppConfig
- getDatabaseConfig
- getCacheConfig
- getQueueConfig
- getLogConfig
- validateConfig
- getEnvVars
- clearCache
- getConfigs
- getPublicConfig
- updateConfig
- batchUpdateConfigs
- resetConfig
- getConfigHistory
- validateConfigValue

### DownloadManagementService.php

- getDownloadHistory
- retryDownload
- getDownloadStatistics
- createDownloadLink
- secureDownload
- createBatchDownload
- cleanupDownloads

### FileService.php

- uploadFile
- getUserFiles
- getFileDetail
- deleteFile
- getDownloadUrl

### ImageService.php

- generateImage
- getImageStatus
- getImageResult
- batchGenerateImages

### LogService.php

- getLogs
- readLog
- analyzeLog
- cleanLogs
- downloadLog
- getLogConfig
- getSystemLogs
- getUserActionLogs
- getAiCallLogs
- getErrorLogs
- resolveError
- exportLogs

### ModelManagementService.php

- getAvailableModels
- getModelDetail
- testModel
- getUserUsageStats
- manageFavorite
- getUserFavorites
- switchUserModel

### ModelService.php

- getAvailableModels
- getModelDetails
- invokeModel
- getModelUsageStats
- updateModelConfig
- getUserModelUsage

### MusicService.php

- generateMusic
- getMusicStatus
- getMusicResult
- batchGenerateMusic

### NotificationService.php

- getNotificationConfig

### PermissionService.php

- checkMultiplePermissions
- getUserPermissions
- getUserRoles
- getRolePermissions
- assignRole
- removeRole
- getRoles
- getPermissions
- createRole
- updateRolePermissions
- getPermissionStats
- checkResourceAccess
- getUserPermissionDetails
- grantPermissions
- revokePermissions
- getPermissionHistory

### PointsService.php

- recharge
- freezePoints
- releasePoints
- consumePoints
- handleTimeoutTransactions
- checkPoints
- confirmPointsUsage
- refundPoints
- refundPointsByFreezeId

### PointsTransactionService.php

- handleSuccess
- handleFailure
- validateBalance
- getBalance
- getTransactions

### ProjectManagementService.php

- createTask
- manageCollaboration
- getProjectProgress
- assignResources
- getProjectStatistics
- getProjectMilestones

### ProjectService.php

- checkAntiSpam
- createWithStory
- confirmTitle
- updateStatus
- createSimpleProject
- updateProject
- deleteProject
- getUserProjects
- getProjectDetail
- getProjectList

### PublicationService.php

- publishWork
- getPublicationStatus
- updatePublication
- unpublishWork
- getUserPublications
- getPublicationPlaza
- getPublicationDetail

### RecommendationService.php

- getContentRecommendations
- getUserRecommendations
- getTopicRecommendations
- submitFeedback
- getUserPreferences
- updateUserPreferences
- getRecommendationAnalytics
- getPersonalizedRecommendations

### ResourceManagementService.php

- createGenerationTask
- getResourceStatus
- getResourceList
- deleteResource

### ReviewService.php

- submitReview
- getReviewStatus
- submitAppeal
- getUserReviews
- getQueueStatus
- getReviewGuidelines
- performPreCheck

### SearchService.php

- globalSearch
- getSearchSuggestions

### SocialService.php

- manageFollow
- getFollowList
- manageLike
- createComment
- getComments
- shareContent
- getSocialFeed
- getNotifications
- markNotificationsRead

### SoundService.php

- generateSound
- getSoundStatus
- getSoundResult
- batchGenerateSounds

### StoryService.php

- generateStory
- getStoryStatus

### StyleService.php

- createStyle
- updateRating
- getRecommendedStyles
- searchStyles
- getCategoryStats

### TaskManagementService.php

- cancelTask
- retryTask
- getBatchTaskStatus
- getBatchTaskStatusByBatchId
- getTimeoutConfig
- getRecoveryStatus

### TemplateService.php

- createTemplate
- useTemplate
- getTemplateMarketplace
- getUserTemplates
- getTemplateDetail
- updateTemplate
- deleteTemplate

### UserGrowthService.php

- getUserGrowthProfile
- getLeaderboard
- completeAchievement
- getDailyTasks
- completeDailyTask
- getGrowthHistory
- getGrowthStatistics
- setUserGoals
- getGrowthRecommendations
- getUserMilestones

### UserService.php

- updatePreferences
- getUserStats
- updateProfile
- resetPreferences

### VersionControlService.php

- createVersion
- getVersionHistory
- getVersionDetail
- setCurrentVersion
- deleteVersion
- compareVersions

### VideoService.php

- generateVideo
- getVideoStatus
- getVideoResult

### VoiceService.php

- synthesizeVoice
- getVoiceStatus
- batchSynthesizeVoices
- cloneVoice
- getVoiceCloneStatus
- customVoice
- getVoiceCustomStatus

### WebSocketEventService.php

- pushAiGenerationProgress
- pushAiGenerationCompleted
- pushAiGenerationFailed
- pushPointsChanged
- pushCustomEvent
- pushSystemNotification
- pushToMultipleUsers
- pushBroadcast

### WebSocketService.php

- authenticateConnection
- getUserSessions
- disconnectSession
- getServerStatus
- pushMessage
- pushToUser
- cleanupTimeoutSessions
- generateAuthToken

### WorkPublishPermissionService.php

- checkPublishPermission
- checkResourcePublishPermission
- mapModuleTypeToWorkType

### WorkPublishService.php

- publishWork
- getMyWorks
- getGallery

### WorkflowService.php

- executeWorkflow
- getExecutionStatus
- getWorkflows
- getWorkflow
- updateWorkflow
- deleteWorkflow
- pauseWorkflow
- resumeWorkflow
- getExecutionHistory
- getWorkflowStats
- getWorkflowDetail
- executeWorkflowWithData
- getExecutionStatusWithAuth
- provideStepInput
- cancelExecution
- getExecutionHistoryWithFilters

## 简化列表

1. AiGenerationService.php -> generateText
2. AiGenerationService.php -> getTaskStatus
3. AiGenerationService.php -> getUserTasks
4. AiGenerationService.php -> retryTask
5. AiLoadBalancingService.php -> distributeTasks
6. AiModelService.php -> switchUserModel
7. AiModelService.php -> getPerformanceMetrics
8. AiModelService.php -> checkServiceHealth
9. AiModelService.php -> getUserDefaultModel
10. AiModelService.php -> getModelByPlatform
11. AiPlatformFallbackService.php -> executeFallback
12. AiPlatformFallbackService.php -> shouldFallback
13. AiPlatformHealthService.php -> checkPlatformHealth
14. AiPlatformHealthService.php -> checkAllPlatformsHealth
15. AiPlatformHealthService.php -> getPlatformAvailability
16. AiPlatformHealthService.php -> recordPerformanceMetric
17. AiPlatformHealthService.php -> getHealthSummary
18. AiPlatformHealthService.php -> checkAlertConditions
19. AiPlatformHealthService.php -> getPlatformComparison
20. AiPlatformHealthService.php -> getBusinessPlatforms
21. AiPlatformSelectionService.php -> selectOptimalPlatform
22. AiPlatformSelectionService.php -> validateBusinessType
23. AiPlatformSelectionService.php -> getSupportedPlatforms
24. AiPlatformSelectionService.php -> isPlatformSupported
25. AiTaskService.php -> cancelTask
26. AiTaskService.php -> getBatchTaskStatus
27. AiTaskService.php -> getTaskRecoveryStatus
28. AiTaskService.php -> getTimeoutConfig
29. AudioService.php -> mixAudio
30. AudioService.php -> getAudioMixStatus
31. AudioService.php -> enhanceAudio
32. AudioService.php -> getAudioEnhanceStatus
33. AuthService.php -> logout
34. AuthService.php -> forgotPassword
35. AuthService.php -> resetPassword
36. AuthService.php -> refreshToken
37. BatchService.php -> startBatch
38. BatchService.php -> cancelBatch
39. BatchService.php -> getBatchResults
40. BatchService.php -> getBatchList
41. BatchService.php -> retryFailedItems
42. BatchService.php -> getBatchStats
43. BatchService.php -> generateResources
44. BatchService.php -> getResourcesStatus
45. CacheService.php -> delete
46. CacheService.php -> setMultiple
47. CacheService.php -> getMultiple
48. CacheService.php -> flush
49. CacheService.php -> healthCheck
50. CacheService.php -> setWithTags
51. CacheService.php -> flushTags
52. CacheService.php -> clearCache
53. CacheService.php -> warmupCache
54. CacheService.php -> getKeys
55. CacheService.php -> getValue
56. CacheService.php -> setValue
57. CacheService.php -> deleteKeys
58. CacheService.php -> getConfig
59. CacheService.php -> getStats
60. CharacterService.php -> getCategories
61. CharacterService.php -> bindCharacter
62. CharacterService.php -> unbindCharacter
63. CharacterService.php -> updateBinding
64. CharacterService.php -> getUserBindings
65. CharacterService.php -> getCharacters
66. CharacterService.php -> getCharacterDetail
67. CharacterService.php -> getRecommendations
68. CharacterService.php -> generateCharacter
69. ConfigService.php -> set
70. ConfigService.php -> getAll
71. ConfigService.php -> getAppConfig
72. ConfigService.php -> getDatabaseConfig
73. ConfigService.php -> getCacheConfig
74. ConfigService.php -> getQueueConfig
75. ConfigService.php -> getLogConfig
76. ConfigService.php -> validateConfig
77. ConfigService.php -> getEnvVars
78. ConfigService.php -> clearCache
79. ConfigService.php -> getConfigs
80. ConfigService.php -> getPublicConfig
81. ConfigService.php -> updateConfig
82. ConfigService.php -> batchUpdateConfigs
83. ConfigService.php -> resetConfig
84. ConfigService.php -> getConfigHistory
85. ConfigService.php -> validateConfigValue
86. DownloadManagementService.php -> getDownloadHistory
87. DownloadManagementService.php -> retryDownload
88. DownloadManagementService.php -> getDownloadStatistics
89. DownloadManagementService.php -> createDownloadLink
90. DownloadManagementService.php -> secureDownload
91. DownloadManagementService.php -> createBatchDownload
92. DownloadManagementService.php -> cleanupDownloads
93. FileService.php -> uploadFile
94. FileService.php -> getUserFiles
95. FileService.php -> getFileDetail
96. FileService.php -> deleteFile
97. FileService.php -> getDownloadUrl
98. ImageService.php -> generateImage
99. ImageService.php -> getImageStatus
100. ImageService.php -> getImageResult
101. ImageService.php -> batchGenerateImages
102. LogService.php -> getLogs
103. LogService.php -> readLog
104. LogService.php -> analyzeLog
105. LogService.php -> cleanLogs
106. LogService.php -> downloadLog
107. LogService.php -> getLogConfig
108. LogService.php -> getSystemLogs
109. LogService.php -> getUserActionLogs
110. LogService.php -> getAiCallLogs
111. LogService.php -> getErrorLogs
112. LogService.php -> resolveError
113. LogService.php -> exportLogs
114. ModelManagementService.php -> getAvailableModels
115. ModelManagementService.php -> getModelDetail
116. ModelManagementService.php -> testModel
117. ModelManagementService.php -> getUserUsageStats
118. ModelManagementService.php -> manageFavorite
119. ModelManagementService.php -> getUserFavorites
120. ModelManagementService.php -> switchUserModel
121. ModelService.php -> getAvailableModels
122. ModelService.php -> getModelDetails
123. ModelService.php -> invokeModel
124. ModelService.php -> getModelUsageStats
125. ModelService.php -> updateModelConfig
126. ModelService.php -> getUserModelUsage
127. MusicService.php -> generateMusic
128. MusicService.php -> getMusicStatus
129. MusicService.php -> getMusicResult
130. MusicService.php -> batchGenerateMusic
131. NotificationService.php -> getNotificationConfig
132. PermissionService.php -> checkMultiplePermissions
133. PermissionService.php -> getUserPermissions
134. PermissionService.php -> getUserRoles
135. PermissionService.php -> getRolePermissions
136. PermissionService.php -> assignRole
137. PermissionService.php -> removeRole
138. PermissionService.php -> getRoles
139. PermissionService.php -> getPermissions
140. PermissionService.php -> createRole
141. PermissionService.php -> updateRolePermissions
142. PermissionService.php -> getPermissionStats
143. PermissionService.php -> checkResourceAccess
144. PermissionService.php -> getUserPermissionDetails
145. PermissionService.php -> grantPermissions
146. PermissionService.php -> revokePermissions
147. PermissionService.php -> getPermissionHistory
148. PointsService.php -> recharge
149. PointsService.php -> freezePoints
150. PointsService.php -> releasePoints
151. PointsService.php -> consumePoints
152. PointsService.php -> handleTimeoutTransactions
153. PointsService.php -> checkPoints
154. PointsService.php -> confirmPointsUsage
155. PointsService.php -> refundPoints
156. PointsService.php -> refundPointsByFreezeId
157. PointsTransactionService.php -> handleSuccess
158. PointsTransactionService.php -> handleFailure
159. PointsTransactionService.php -> validateBalance
160. PointsTransactionService.php -> getBalance
161. PointsTransactionService.php -> getTransactions
162. ProjectManagementService.php -> createTask
163. ProjectManagementService.php -> manageCollaboration
164. ProjectManagementService.php -> getProjectProgress
165. ProjectManagementService.php -> assignResources
166. ProjectManagementService.php -> getProjectStatistics
167. ProjectManagementService.php -> getProjectMilestones
168. ProjectService.php -> checkAntiSpam
169. ProjectService.php -> createWithStory
170. ProjectService.php -> confirmTitle
171. ProjectService.php -> updateStatus
172. ProjectService.php -> createSimpleProject
173. ProjectService.php -> updateProject
174. ProjectService.php -> deleteProject
175. ProjectService.php -> getUserProjects
176. ProjectService.php -> getProjectDetail
177. ProjectService.php -> getProjectList
178. PublicationService.php -> publishWork
179. PublicationService.php -> getPublicationStatus
180. PublicationService.php -> updatePublication
181. PublicationService.php -> unpublishWork
182. PublicationService.php -> getUserPublications
183. PublicationService.php -> getPublicationPlaza
184. PublicationService.php -> getPublicationDetail
185. RecommendationService.php -> getContentRecommendations
186. RecommendationService.php -> getUserRecommendations
187. RecommendationService.php -> getTopicRecommendations
188. RecommendationService.php -> submitFeedback
189. RecommendationService.php -> getUserPreferences
190. RecommendationService.php -> updateUserPreferences
191. RecommendationService.php -> getRecommendationAnalytics
192. RecommendationService.php -> getPersonalizedRecommendations
193. ResourceManagementService.php -> createGenerationTask
194. ResourceManagementService.php -> getResourceStatus
195. ResourceManagementService.php -> getResourceList
196. ResourceManagementService.php -> deleteResource
197. ReviewService.php -> submitReview
198. ReviewService.php -> getReviewStatus
199. ReviewService.php -> submitAppeal
200. ReviewService.php -> getUserReviews
201. ReviewService.php -> getQueueStatus
202. ReviewService.php -> getReviewGuidelines
203. ReviewService.php -> performPreCheck
204. SearchService.php -> globalSearch
205. SearchService.php -> getSearchSuggestions
206. SocialService.php -> manageFollow
207. SocialService.php -> getFollowList
208. SocialService.php -> manageLike
209. SocialService.php -> createComment
210. SocialService.php -> getComments
211. SocialService.php -> shareContent
212. SocialService.php -> getSocialFeed
213. SocialService.php -> getNotifications
214. SocialService.php -> markNotificationsRead
215. SoundService.php -> generateSound
216. SoundService.php -> getSoundStatus
217. SoundService.php -> getSoundResult
218. SoundService.php -> batchGenerateSounds
219. StoryService.php -> generateStory
220. StoryService.php -> getStoryStatus
221. StyleService.php -> createStyle
222. StyleService.php -> updateRating
223. StyleService.php -> getRecommendedStyles
224. StyleService.php -> searchStyles
225. StyleService.php -> getCategoryStats
226. TaskManagementService.php -> cancelTask
227. TaskManagementService.php -> retryTask
228. TaskManagementService.php -> getBatchTaskStatus
229. TaskManagementService.php -> getBatchTaskStatusByBatchId
230. TaskManagementService.php -> getTimeoutConfig
231. TaskManagementService.php -> getRecoveryStatus
232. TemplateService.php -> createTemplate
233. TemplateService.php -> useTemplate
234. TemplateService.php -> getTemplateMarketplace
235. TemplateService.php -> getUserTemplates
236. TemplateService.php -> getTemplateDetail
237. TemplateService.php -> updateTemplate
238. TemplateService.php -> deleteTemplate
239. UserGrowthService.php -> getUserGrowthProfile
240. UserGrowthService.php -> getLeaderboard
241. UserGrowthService.php -> completeAchievement
242. UserGrowthService.php -> getDailyTasks
243. UserGrowthService.php -> completeDailyTask
244. UserGrowthService.php -> getGrowthHistory
245. UserGrowthService.php -> getGrowthStatistics
246. UserGrowthService.php -> setUserGoals
247. UserGrowthService.php -> getGrowthRecommendations
248. UserGrowthService.php -> getUserMilestones
249. UserService.php -> updatePreferences
250. UserService.php -> getUserStats
251. UserService.php -> updateProfile
252. UserService.php -> resetPreferences
253. VersionControlService.php -> createVersion
254. VersionControlService.php -> getVersionHistory
255. VersionControlService.php -> getVersionDetail
256. VersionControlService.php -> setCurrentVersion
257. VersionControlService.php -> deleteVersion
258. VersionControlService.php -> compareVersions
259. VideoService.php -> generateVideo
260. VideoService.php -> getVideoStatus
261. VideoService.php -> getVideoResult
262. VoiceService.php -> synthesizeVoice
263. VoiceService.php -> getVoiceStatus
264. VoiceService.php -> batchSynthesizeVoices
265. VoiceService.php -> cloneVoice
266. VoiceService.php -> getVoiceCloneStatus
267. VoiceService.php -> customVoice
268. VoiceService.php -> getVoiceCustomStatus
269. WebSocketEventService.php -> pushAiGenerationProgress
270. WebSocketEventService.php -> pushAiGenerationCompleted
271. WebSocketEventService.php -> pushAiGenerationFailed
272. WebSocketEventService.php -> pushPointsChanged
273. WebSocketEventService.php -> pushCustomEvent
274. WebSocketEventService.php -> pushSystemNotification
275. WebSocketEventService.php -> pushToMultipleUsers
276. WebSocketEventService.php -> pushBroadcast
277. WebSocketService.php -> authenticateConnection
278. WebSocketService.php -> getUserSessions
279. WebSocketService.php -> disconnectSession
280. WebSocketService.php -> getServerStatus
281. WebSocketService.php -> pushMessage
282. WebSocketService.php -> pushToUser
283. WebSocketService.php -> cleanupTimeoutSessions
284. WebSocketService.php -> generateAuthToken
285. WorkPublishPermissionService.php -> checkPublishPermission
286. WorkPublishPermissionService.php -> checkResourcePublishPermission
287. WorkPublishPermissionService.php -> mapModuleTypeToWorkType
288. WorkPublishService.php -> publishWork
289. WorkPublishService.php -> getMyWorks
290. WorkPublishService.php -> getGallery
291. WorkflowService.php -> executeWorkflow
292. WorkflowService.php -> getExecutionStatus
293. WorkflowService.php -> getWorkflows
294. WorkflowService.php -> getWorkflow
295. WorkflowService.php -> updateWorkflow
296. WorkflowService.php -> deleteWorkflow
297. WorkflowService.php -> pauseWorkflow
298. WorkflowService.php -> resumeWorkflow
299. WorkflowService.php -> getExecutionHistory
300. WorkflowService.php -> getWorkflowStats
301. WorkflowService.php -> getWorkflowDetail
302. WorkflowService.php -> executeWorkflowWithData
303. WorkflowService.php -> getExecutionStatusWithAuth
304. WorkflowService.php -> provideStepInput
305. WorkflowService.php -> cancelExecution
306. WorkflowService.php -> getExecutionHistoryWithFilters
