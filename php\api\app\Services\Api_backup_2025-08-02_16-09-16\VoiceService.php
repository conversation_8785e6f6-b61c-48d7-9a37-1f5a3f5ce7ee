<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\CharacterLibrary;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 语音生成服务
 * 第2D2阶段：语音生成模块
 */
class VoiceService extends Service
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 合成语音
     */
    public function synthesizeVoice(int $userId, string $text, ?int $characterId = null, ?int $projectId = null, array $synthesisParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取角色信息
            $character = null;
            if ($characterId) {
                $character = CharacterLibrary::find($characterId);
                if (!$character) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '角色不存在',
                        'data' => []
                    ];
                }
            }

            // 获取模型配置
            $platform = $synthesisParams['platform'] ?? 'minimax';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_VOICE_SYNTHESIS);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的语音合成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '语音合成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建合成参数
            $enhancedParams = $this->buildSynthesisParams($text, $character, $synthesisParams);

            // 计算预估成本
            $estimatedCost = $this->calculateVoiceCost($model, $text, $synthesisParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'voice_synthesis',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_VOICE_SYNTHESIS,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'text' => $text,
                    'character_id' => $characterId,
                    'voice_id' => $synthesisParams['voice_id'] ?? null,
                    'speed' => $synthesisParams['speed'] ?? 1.0,
                    'pitch' => $synthesisParams['pitch'] ?? 1.0,
                    'emotion' => $synthesisParams['emotion'] ?? 'neutral',
                    'language' => $synthesisParams['language'] ?? 'zh-CN'
                ],
                'generation_params' => $enhancedParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeVoiceSynthesis($task);

            Log::info('语音合成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '语音合成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'text' => substr($text, 0, 100),
                'character_id' => $characterId,
                'project_id' => $projectId,
                'synthesis_params_count' => is_array($synthesisParams) ? count($synthesisParams) : 0,
            ];

            Log::error('语音合成失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '语音合成失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取语音合成状态
     */
    public function getVoiceStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_VOICE_SYNTHESIS)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['audio_url'] = $task->output_data['audio_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];
        } catch (\Exception $e) {
            $services_data = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取语音合成状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取语音合成状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 批量合成语音
     */
    public function batchSynthesizeVoices(int $userId, array $texts, ?int $projectId = null, array $commonParams = []): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $taskIds = [];
            $totalCost = 0;

            foreach ($texts as $text) {
                $result = $this->synthesizeVoice($userId, $text, null, $projectId, $commonParams);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $taskIds[] = $result['data']['task_id'];
                    $totalCost += $result['data']['estimated_cost'];
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量语音合成任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'task_ids' => $taskIds,
                    'total_count' => count($taskIds),
                    'estimated_cost' => number_format($totalCost, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            $services_data = [
                'user_id' => $userId,
                'texts_count' => count($texts),
                'project_id' => $projectId,
                'common_params_count' => is_array($commonParams) ? count($commonParams) : 0,
            ];

            Log::error('批量语音合成失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量语音合成失败',
                'data' => null
            ];
        }
    }

    /**
     * 构建合成参数
     */
    private function buildSynthesisParams(string $text, ?CharacterLibrary $character, array $params): array
    {
        $enhancedParams = $params;

        // 添加角色音色信息
        if ($character && $character->voice_settings) {
            $voiceSettings = json_decode($character->voice_settings, true);
            $enhancedParams['voice_id'] = $voiceSettings['voice_id'] ?? $params['voice_id'] ?? null;
            $enhancedParams['emotion'] = $voiceSettings['emotion'] ?? $params['emotion'] ?? 'neutral';
        }

        // 文本长度影响参数
        $textLength = mb_strlen($text);
        if ($textLength > 1000) {
            $enhancedParams['speed'] = min(($params['speed'] ?? 1.0) * 1.1, 2.0); // 长文本稍微加速
        }

        return $enhancedParams;
    }

    /**
     * 计算语音合成成本
     */
    private function calculateVoiceCost(AiModelConfig $model, string $text, array $params): float
    {
        $textLength = mb_strlen($text);
        $baseCost = $model->cost_per_token;
        
        // 文本长度影响成本（每100字符为基准）
        $lengthMultiplier = ceil($textLength / 100);
        
        // 情感和音色影响成本
        $emotion = $params['emotion'] ?? 'neutral';
        $emotionMultiplier = $emotion === 'neutral' ? 1.0 : 1.2;

        return round($baseCost * $lengthMultiplier * $emotionMultiplier, 4);
    }

    /**
     * 执行语音合成
     */
    private function executeVoiceSynthesis(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'voice_synthesis', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'voice_synthesis_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'voice_synthesis_error', $task->id);

            Log::error('语音合成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/minimax/v1/tts';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 30);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'text' => $task->input_data['text'],
                'voice_id' => $task->input_data['voice_id'],
                'speed' => $task->input_data['speed'],
                'pitch' => $task->input_data['pitch'],
                'emotion' => $task->input_data['emotion'],
                'language' => $task->input_data['language']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'data' => [
                        'audio_url' => $data['data']['audio_url'] ?? '',
                        'duration' => $data['data']['duration'] ?? 0,
                        'file_size' => $data['data']['file_size'] ?? '1.2MB',
                        'format' => 'mp3',
                        'sample_rate' => $data['data']['sample_rate'] ?? '44100Hz'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 克隆音色
     * 第2D3阶段：音色克隆功能
     */
    public function cloneVoice(int $userId, string $sourceAudioUrl, ?int $characterId = null, ?int $projectId = null, array $cloneParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取角色信息
            $character = null;
            if ($characterId) {
                $character = CharacterLibrary::find($characterId);
                if (!$character) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '角色不存在',
                        'data' => []
                    ];
                }
            }

            // 获取模型配置
            $platform = $cloneParams['platform'] ?? 'minimax';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_VOICE_SYNTHESIS);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::CLINE_VOICE_NOT_FOUND,
                    'message' => '没有可用的音色克隆模型',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateVoiceCloneCost($model);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'voice_clone',
                null,
                600 // 10分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => 'voice_clone',
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'source_audio_url' => $sourceAudioUrl,
                    'voice_name' => $cloneParams['voice_name'],
                    'description' => $cloneParams['description'] ?? null,
                    'character_id' => $characterId
                ],
                'generation_params' => $cloneParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            Log::info('音色克隆任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音色克隆任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'source_audio_url' => $sourceAudioUrl,
                'character_id' => $characterId,
                'project_id' => $projectId,
                'clone_params_count' => is_array($cloneParams) ? count($cloneParams) : 0,
            ];

            Log::error('音色克隆失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音色克隆失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音色克隆状态
     */
    public function getVoiceCloneStatus(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'voice_clone')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['voice_id'] = $task->output_data['voice_id'] ?? '';
                $data['voice_name'] = $task->input_data['voice_name'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取音色克隆状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取音色克隆状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 自定义音色生成
     */
    public function customVoice(int $userId, ?int $characterId = null, ?int $projectId = null, array $customParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $customParams['platform'] ?? 'minimax';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_VOICE_SYNTHESIS);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的音色生成模型',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateVoiceCustomCost($model);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'voice_custom',
                null,
                600 // 10分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => 'voice_custom',
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'voice_config' => $customParams['voice_config'],
                    'voice_name' => $customParams['voice_name'],
                    'character_id' => $characterId
                ],
                'generation_params' => $customParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            Log::info('自定义音色生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '自定义音色生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'character_id' => $characterId,
                'project_id' => $projectId,
                'custom_params_count' => is_array($customParams) ? count($customParams) : 0,
            ];

            Log::error('自定义音色生成失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '自定义音色生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取自定义音色状态
     */
    public function getVoiceCustomStatus(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'voice_custom')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['voice_id'] = $task->output_data['voice_id'] ?? '';
                $data['voice_name'] = $task->input_data['voice_name'] ?? '';
                $data['voice_config'] = $task->input_data['voice_config'] ?? [];
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取自定义音色状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取自定义音色状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 计算音色克隆成本
     */
    private function calculateVoiceCloneCost(AiModelConfig $model): float
    {
        return round($model->cost_per_token * 500, 4); // 音色克隆固定成本
    }

    /**
     * 计算自定义音色成本
     */
    private function calculateVoiceCustomCost(AiModelConfig $model): float
    {
        return round($model->cost_per_token * 300, 4); // 自定义音色固定成本
    }
}
