<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 视频生成服务
 * 第2D2阶段：视频生成模块
 */
class VideoService extends Service
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成视频
     */
    public function generateVideo(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'kling';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_VIDEO_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的视频生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '视频生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildVideoPrompt($prompt, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateVideoCost($model, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'video_generation',
                null,
                1800 // 30分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_VIDEO_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'duration' => $generationParams['duration'] ?? 5,
                    'aspect_ratio' => $generationParams['aspect_ratio'] ?? '16:9',
                    'quality' => $generationParams['quality'] ?? 'standard',
                    'fps' => $generationParams['fps'] ?? 30,
                    'style' => $generationParams['style'] ?? null
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeVideoGeneration($task);

            Log::info('视频生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '视频生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'estimated_duration' => $this->getEstimatedDuration($generationParams),
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('视频生成失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '视频生成失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取视频生成状态
     */
    public function getVideoStatus(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_VIDEO_GENERATION)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'platform' => $task->platform,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['video_url'] = $task->output_data['video_url'] ?? '';
                $data['thumbnail_url'] = $task->output_data['thumbnail_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['resolution'] = $task->output_data['resolution'] ?? '';
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取视频状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取视频状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取视频生成结果
     */
    public function getVideoResult(int $taskId, int $userId): array
    {
        try {
            // 原服务层的业务代码逻辑
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', AiGenerationTask::TYPE_VIDEO_GENERATION)
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            if ($task->status !== AiGenerationTask::STATUS_COMPLETED) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '任务尚未完成',
                    'data' => []
                ];
            }

            $data = [
                'task_id' => $task->id,
                'video_url' => $task->output_data['video_url'] ?? '',
                'thumbnail_url' => $task->output_data['thumbnail_url'] ?? '',
                'preview_url' => $task->output_data['preview_url'] ?? '',
                'metadata' => [
                    'duration' => $task->output_data['duration'] ?? 0,
                    'resolution' => $task->output_data['resolution'] ?? '',
                    'fps' => $task->output_data['fps'] ?? 30,
                    'format' => 'mp4',
                    'file_size' => $task->output_data['file_size'] ?? '',
                    'bitrate' => $task->output_data['bitrate'] ?? ''
                ],
                'download_info' => [
                    'direct_url' => $task->output_data['video_url'] ?? '',
                    'expires_at' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s')
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取视频结果失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取视频结果失败',
                'data' => null
            ];
        }
    }

    /**
     * 构建视频提示词
     */
    private function buildVideoPrompt(string $prompt, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加风格信息
        if (!empty($params['style'])) {
            $enhancedPrompt .= "\n\n风格要求：" . $params['style'];
        }

        // 添加质量要求
        $quality = $params['quality'] ?? 'standard';
        $qualityMap = [
            'standard' => '标准画质，清晰流畅',
            'hd' => '高清画质，细节丰富',
            '4k' => '4K超高清，专业级画质'
        ];
        $enhancedPrompt .= "\n\n画质要求：" . $qualityMap[$quality];

        // 添加时长要求
        $duration = $params['duration'] ?? 5;
        $enhancedPrompt .= "\n\n视频时长：{$duration}秒";

        return $enhancedPrompt;
    }

    /**
     * 计算视频生成成本
     */
    private function calculateVideoCost(AiModelConfig $model, array $params): float
    {
        $baseCost = $model->cost_per_token;
        
        // 时长影响成本
        $duration = $params['duration'] ?? 5;
        $durationMultiplier = $duration / 5; // 以5秒为基准
        
        // 质量影响成本
        $quality = $params['quality'] ?? 'standard';
        $qualityMultiplier = [
            'standard' => 1.0,
            'hd' => 2.0,
            '4k' => 4.0
        ][$quality] ?? 1.0;

        return round($baseCost * $durationMultiplier * $qualityMultiplier, 4);
    }

    /**
     * 获取预估生成时间
     */
    private function getEstimatedDuration(array $params): int
    {
        $duration = $params['duration'] ?? 5;
        $quality = $params['quality'] ?? 'standard';
        
        // 基础生成时间（秒）
        $baseTime = $duration * 30; // 每秒视频需要30秒生成时间
        
        // 质量影响生成时间
        $qualityMultiplier = [
            'standard' => 1.0,
            'hd' => 1.5,
            '4k' => 2.5
        ][$quality] ?? 1.0;

        return (int)($baseTime * $qualityMultiplier);
    }

    /**
     * 执行视频生成
     */
    private function executeVideoGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'video_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'video_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'video_generation_error', $task->id);

            Log::error('视频生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/kling/v1/videos/generations';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 60);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'prompt' => $task->input_data['enhanced_prompt'],
                'duration' => $task->input_data['duration'],
                'aspect_ratio' => $task->input_data['aspect_ratio'],
                'quality' => $task->input_data['quality'],
                'fps' => $task->input_data['fps']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'data' => [
                        'video_url' => $data['data']['video_url'] ?? '',
                        'thumbnail_url' => $data['data']['thumbnail_url'] ?? '',
                        'preview_url' => $data['data']['preview_url'] ?? '',
                        'duration' => $data['data']['duration'] ?? $task->input_data['duration'],
                        'resolution' => $data['data']['resolution'] ?? '1920x1080',
                        'fps' => $data['data']['fps'] ?? $task->input_data['fps'],
                        'file_size' => $data['data']['file_size'] ?? '15.2MB',
                        'bitrate' => $data['data']['bitrate'] ?? '2000kbps'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }
}
