<?php

/**
 * 检查Services/Api目录下所有服务文件中的public方法是否包含'services_data' => $services_data,
 */

class ServicesDataChecker
{
    private $servicesPath = 'D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\Api';
    private $missingMethods = [];
    private $totalFiles = 0;
    private $totalMethods = 0;
    private $checkedMethods = 0;

    public function checkAllServices()
    {
        echo "开始检查Services/Api目录下的所有服务文件...\n";
        echo "检查路径: {$this->servicesPath}\n";
        echo str_repeat('=', 80) . "\n";

        $files = glob($this->servicesPath . '/*.php');
        $this->totalFiles = count($files);
        
        echo "发现 {$this->totalFiles} 个服务文件\n\n";

        foreach ($files as $file) {
            $this->checkServiceFile($file);
        }

        $this->generateReport();
    }

    private function checkServiceFile($filePath)
    {
        $fileName = basename($filePath);
        echo "检查服务文件: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if (!$content) {
            echo "  错误: 无法读取文件\n";
            return;
        }

        // 获取所有public方法
        $methods = $this->extractPublicMethods($content);
        $this->totalMethods += count($methods);
        
        echo "  发现 " . count($methods) . " 个public方法\n";
        
        foreach ($methods as $method) {
            $this->checkMethodForServicesData($content, $method, $fileName);
        }
        
        echo "\n";
    }

    private function extractPublicMethods($content)
    {
        // 匹配所有public方法，排除构造函数
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)/', $content, $matches);
        
        $methods = [];
        foreach ($matches[1] as $methodName) {
            // 排除构造函数
            if ($methodName !== '__construct') {
                $methods[] = $methodName;
            }
        }
        
        return $methods;
    }

    private function checkMethodForServicesData($content, $methodName, $fileName)
    {
        $this->checkedMethods++;
        
        // 获取方法的完整内容
        $pattern = '/public\s+function\s+' . preg_quote($methodName) . '\s*\([^)]*\)\s*\{/s';
        
        if (preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
            $startPos = $matches[0][1] + strlen($matches[0][0]);
            
            // 找到方法的结束位置
            $braceCount = 1;
            $pos = $startPos;
            $methodContent = '';
            
            while ($pos < strlen($content) && $braceCount > 0) {
                $char = $content[$pos];
                $methodContent .= $char;
                
                if ($char === '{') {
                    $braceCount++;
                } elseif ($char === '}') {
                    $braceCount--;
                }
                $pos++;
            }
            
            // 移除最后的}
            $methodContent = rtrim($methodContent, '}');
            
            // 检查是否包含'services_data' => $services_data,
            $hasServicesData = strpos($methodContent, "'services_data' => \$services_data,") !== false;
            
            if (!$hasServicesData) {
                $this->missingMethods[] = [
                    'file' => $fileName,
                    'method' => $methodName
                ];
                echo "    ❌ {$methodName}: 未包含 'services_data' => \$services_data,\n";
            } else {
                echo "    ✅ {$methodName}: 包含 'services_data' => \$services_data,\n";
            }
        } else {
            echo "    ❌ {$methodName}: 无法解析方法内容\n";
            $this->missingMethods[] = [
                'file' => $fileName,
                'method' => $methodName
            ];
        }
    }

    private function generateReport()
    {
        echo str_repeat('=', 80) . "\n";
        echo "检查完成报告\n";
        echo str_repeat('=', 80) . "\n";
        echo "总服务文件数量: {$this->totalFiles}\n";
        echo "总public方法数量: {$this->totalMethods}\n";
        echo "已检查方法数量: {$this->checkedMethods}\n";
        echo "缺少'services_data'的方法数量: " . count($this->missingMethods) . "\n\n";
        
        if (empty($this->missingMethods)) {
            echo "🎉 所有public方法都包含'services_data' => \$services_data,！\n";
        } else {
            echo "❌ 以下方法缺少'services_data' => \$services_data,:\n";
            echo str_repeat('-', 80) . "\n";
            
            // 按文件分组显示
            $groupedMethods = [];
            foreach ($this->missingMethods as $missing) {
                $groupedMethods[$missing['file']][] = $missing['method'];
            }
            
            foreach ($groupedMethods as $fileName => $methods) {
                echo "\n📁 {$fileName}:\n";
                foreach ($methods as $method) {
                    echo "   - {$method}\n";
                }
            }
            
            echo "\n" . str_repeat('-', 80) . "\n";
            echo "简化列表 (文件名+方法名):\n";
            foreach ($this->missingMethods as $index => $missing) {
                echo ($index + 1) . ". {$missing['file']} -> {$missing['method']}\n";
            }
        }
        
        // 生成详细报告文件
        $this->writeReportToFile();
    }
    
    private function writeReportToFile()
    {
        $reportContent = "# Services/Api 目录 'services_data' 检查报告\n\n";
        $reportContent .= "## 检查概要\n\n";
        $reportContent .= "- **总服务文件数量**: {$this->totalFiles}\n";
        $reportContent .= "- **总public方法数量**: {$this->totalMethods}\n";
        $reportContent .= "- **缺少'services_data'的方法数量**: " . count($this->missingMethods) . "\n\n";
        
        if (empty($this->missingMethods)) {
            $reportContent .= "🎉 **所有public方法都包含'services_data' => \$services_data,！**\n\n";
        } else {
            $reportContent .= "## ❌ 缺少'services_data' => \$services_data, 的方法\n\n";
            
            // 按文件分组
            $groupedMethods = [];
            foreach ($this->missingMethods as $missing) {
                $groupedMethods[$missing['file']][] = $missing['method'];
            }
            
            foreach ($groupedMethods as $fileName => $methods) {
                $reportContent .= "### {$fileName}\n\n";
                foreach ($methods as $method) {
                    $reportContent .= "- {$method}\n";
                }
                $reportContent .= "\n";
            }
            
            $reportContent .= "## 简化列表\n\n";
            foreach ($this->missingMethods as $index => $missing) {
                $reportContent .= ($index + 1) . ". {$missing['file']} -> {$missing['method']}\n";
            }
        }
        
        file_put_contents('services_data_check_report.md', $reportContent);
        echo "\n详细报告已保存到: services_data_check_report.md\n";
    }
}

// 执行检查
$checker = new ServicesDataChecker();
$checker->checkAllServices();

echo "\n检查完成！\n";
?>