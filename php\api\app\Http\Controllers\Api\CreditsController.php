<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\PointsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 积分高级功能与冻结返还
 */
class CreditsController extends Controller
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * @ApiTitle(积分预检查)
     * @ApiSummary(检查用户积分是否足够进行指定操作)
     * @ApiMethod(POST)
     * @ApiRoute(/api/credits/check)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="amount", type="float", required=true, description="需要检查的积分数量")
     * @ApiParams(name="business_type", type="string", required=true, description="业务类型")
     * @ApiParams(name="business_id", type="int", required=false, description="业务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.sufficient", type="boolean", required=true, description="积分是否充足")
     * @ApiReturnParams (name="data.current_balance", type="decimal", required=true, description="当前积分余额")
     * @ApiReturnParams (name="data.required_amount", type="decimal", required=true, description="所需积分")
     * @ApiReturnParams (name="data.shortage", type="decimal", required=false, description="不足的积分数量")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "积分检查完成",
     *   "data": {
     *     "sufficient": true,
     *     "current_balance": "10.5000",
     *     "required_amount": "2.0000",
     *     "shortage": "0.0000"
     *   }
     * })
     */
    public function checkCredits(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
        if (!$authResult['success']) {
            return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
        }

        // 基于dev-api-guidelines-add.mdc的参数验证（用户ID从认证Token获取，不作为参数）
        $rules = [
            'amount' => 'required|integer|min:1',
            'business_type' => 'required|string|max:50'
        ];

        $messages = [
            'amount.required' => '积分数量不能为空',
            'amount.integer' => '积分数量必须是整数',
            'amount.min' => '积分数量不能小于1',
            'business_type.required' => '业务类型不能为空',
            'business_type.max' => '业务类型不能超过50个字符'
        ];

        $this->validateData($request->all(), $rules, $messages, []);

        $user = $authResult['user'];

        $result = $this->pointsService->checkPoints(
            $user->id,  // 从认证Token获取用户ID，确保安全性
            $request->amount,
            $request->business_type,
            $request->get('business_id') // 可选参数
        );

        if ($result['code'] === ApiCodeEnum::SUCCESS) {
            return $this->successResponse($result['data'], $result['message']);
        } else {
            return $this->errorResponse($result['code'], $result['message'], $result['data']);
        }
    } catch (\Exception $e) {
            Log::error('积分检查失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '积分检查失败', []);
        }
    }

    /**
     * @ApiTitle(积分冻结)
     * @ApiSummary(冻结用户指定数量的积分)
     * @ApiMethod(POST)
     * @ApiRoute(/api/credits/freeze)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="amount", type="float", required=true, description="需要冻结的积分数量")
     * @ApiParams(name="business_type", type="string", required=true, description="业务类型")
     * @ApiParams(name="business_id", type="int", required=false, description="业务ID")
     * @ApiParams(name="timeout_seconds", type="int", required=false, description="冻结超时时间（秒）")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.freeze_id", type="string", required=true, description="冻结记录ID")
     * @ApiReturnParams (name="data.frozen_amount", type="decimal", required=true, description="冻结的积分数量")
     * @ApiReturnParams (name="data.remaining_balance", type="decimal", required=true, description="剩余可用积分")
     * @ApiReturnParams (name="data.expires_at", type="string", required=true, description="冻结过期时间")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "积分冻结成功",
     *   "data": {
     *     "freeze_id": "freeze_123456",
     *     "frozen_amount": "2.0000",
     *     "remaining_balance": "8.5000",
     *     "expires_at": "2024-01-01 12:10:00"
     *   }
     * })
     */
    public function freezeCredits(Request $request)
    {
        try {
            // 先进行认证检查
                $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 基于dev-api-guidelines-add.mdc的参数验证（用户ID从认证Token获取）
            $rules = [
                'amount' => 'required|integer|min:1',
                'business_type' => 'required|string|max:50',
                'business_id' => 'required|string|max:100'
            ];

            $messages = [
                'amount.required' => '积分数量不能为空',
                'amount.integer' => '积分数量必须是整数',
                'amount.min' => '积分数量不能小于1',
                'business_type.required' => '业务类型不能为空',
                'business_type.max' => '业务类型不能超过50个字符',
                'business_id.required' => '业务ID不能为空',
                'business_id.max' => '业务ID不能超过100个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $this->pointsService->freezePoints(
                $user->id,  // 从认证Token获取用户ID
                $request->amount,
                $request->business_type,
                $request->business_id,
                $request->get('timeout_seconds', 600)
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('积分冻结失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '积分冻结失败', []);
        }
    }

    /**
     * @ApiTitle(积分返还)
     * @ApiSummary(返还用户指定数量的积分)
     * @ApiMethod(POST)
     * @ApiRoute(/api/credits/refund)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="amount", type="float", required=true, description="需要返还的积分数量")
     * @ApiParams(name="business_type", type="string", required=true, description="业务类型")
     * @ApiParams(name="business_id", type="int", required=true, description="业务ID")
     * @ApiParams(name="reason", type="string", required=false, description="返还原因")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.transaction_id", type="int", required=true, description="交易记录ID")
     * @ApiReturnParams (name="data.refunded_amount", type="decimal", required=true, description="返还的积分数量")
     * @ApiReturnParams (name="data.current_balance", type="decimal", required=true, description="当前积分余额")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "积分返还成功",
     *   "data": {
     *     "transaction_id": 123,
     *     "refunded_amount": "2.0000",
     *     "current_balance": "12.5000"
     *   }
     * })
     */
    public function refundCredits(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $rules = [
                'freeze_id' => 'required|string|max:100',
                'return_reason' => 'nullable|string|max:200'
            ];

            $messages = [
                'freeze_id.required' => '冻结ID不能为空',
                'freeze_id.string' => '冻结ID必须是字符串',
                'freeze_id.max' => '冻结ID不能超过100个字符',
                'return_reason.max' => '返还原因不能超过200个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];

            // 调用符合dev-api-guidelines-add.mdc标准的方法
            $result = $this->pointsService->refundPointsByFreezeId(
                $user->id,
                $request->freeze_id,
                $request->return_reason ?? 'API手动返还'
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('积分返还失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '积分返还失败', []);
        }
    }


}
