<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 权限服务类
 * 负责用户权限、角色管理和访问控制
 */
class PermissionService extends Service
{
    private $defaultRoles = ['admin', 'user', 'guest', 'moderator', 'editor'];
    private $defaultPermissions = [
        'user.create', 'user.read', 'user.update', 'user.delete',
        'role.create', 'role.read', 'role.update', 'role.delete',
        'permission.create', 'permission.read', 'permission.update', 'permission.delete',
        'ai.generate', 'ai.manage', 'ai.view',
        'system.config', 'system.monitor', 'system.backup'
    ];
    
    /**
     * 检查用户权限
     */
    public function checkPermission($userId, $permission)
    {
        try {
            // 从缓存获取用户权限
            $cacheKey = "user_permissions:{$userId}";
            $userPermissions = Cache::get($cacheKey);
            
            if (!$userPermissions) {
                $userPermissions = $this->getUserPermissions($userId);
                Cache::put($cacheKey, $userPermissions, 3600); // 缓存1小时
            }
            
            $hasPermission = in_array($permission, $userPermissions) || in_array('*', $userPermissions);
            
            // 记录权限检查日志
            Log::info("权限检查", [
                'user_id' => $userId,
                'permission' => $permission,
                'result' => $hasPermission ? 'granted' : 'denied'
            ]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $hasPermission ? '权限验证通过' : '权限不足',
                'data' => [
                    'user_id' => $userId,
                    'permission' => $permission,
                    'has_permission' => $hasPermission,
                    'checked_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'permission' => $permission,
            ];

            Log::error('权限检查失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限检查失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 批量检查权限
     */
    public function checkMultiplePermissions($userId, $permissions)
    {
        try {
            $results = [];
            $userPermissions = $this->getUserPermissions($userId);
            $hasAllPermissions = true;
            
            foreach ($permissions as $permission) {
                $hasPermission = in_array($permission, $userPermissions) || in_array('*', $userPermissions);
                $results[$permission] = $hasPermission;
                
                if (!$hasPermission) {
                    $hasAllPermissions = false;
                }
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $hasAllPermissions ? '所有权限验证通过' : '部分权限不足',
                'data' => [
                    'user_id' => $userId,
                    'permissions' => $results,
                    'has_all_permissions' => $hasAllPermissions,
                    'checked_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'permissions_count' => is_array($permissions) ? count($permissions) : 0,
            ];

            Log::error('批量权限检查失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量权限检查失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取用户权限列表
     */
    public function getUserPermissions($userId)
    {
        try {
            // 模拟获取用户权限（实际项目中应从数据库获取）
            $userRoles = $this->getUserRoles($userId);
            $permissions = [];
            
            foreach ($userRoles as $role) {
                $rolePermissions = $this->getRolePermissions($role);
                $permissions = array_merge($permissions, $rolePermissions);
            }
            
            // 去重并排序
            $permissions = array_unique($permissions);
            sort($permissions);
            
            return $permissions;
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
            ];

            Log::error('操作失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '操作失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取用户角色
     */
    public function getUserRoles($userId)
    {
        try {
            // 模拟用户角色数据
            $userRoles = [
                1 => ['admin'],
                2 => ['user'],
                3 => ['editor', 'user'],
                4 => ['moderator', 'user'],
                5 => ['guest']
            ];
            
            return $userRoles[$userId] ?? ['guest'];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
            ];

            Log::error('操作失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '操作失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取角色权限
     */
    public function getRolePermissions($role)
    {
        try {
            // 模拟角色权限映射
            $rolePermissions = [
                'admin' => ['*'], // 管理员拥有所有权限
                'editor' => [
                    'user.read', 'user.update',
                    'ai.generate', 'ai.view',
                    'system.monitor'
                ],
                'moderator' => [
                    'user.read', 'user.update',
                    'ai.view', 'ai.manage'
                ],
                'user' => [
                    'user.read', 'user.update',
                    'ai.generate', 'ai.view'
                ],
                'guest' => [
                    'user.read',
                    'ai.view'
                ]
            ];
            
            return $rolePermissions[$role] ?? [];
        } catch (\Exception $e) {
            $services_data = [
                'role' => $role,
            ];

            Log::error('操作失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '操作失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 分配角色给用户
     */
    public function assignRole($userId, $role)
    {
        try {
            if (!in_array($role, $this->defaultRoles)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '无效的角色：' . $role,
                    'data' => null
                ];
            }
            
            // 模拟分配角色
            Log::info("角色分配", [
                'user_id' => $userId,
                'role' => $role,
                'assigned_at' => now()
            ]);
            
            // 清除用户权限缓存
            Cache::forget("user_permissions:{$userId}");
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色分配成功',
                'data' => [
                    'user_id' => $userId,
                    'role' => $role,
                    'assigned_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'role' => $role,
            ];

            Log::error('角色分配失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色分配失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 移除用户角色
     */
    public function removeRole($userId, $role)
    {
        try {
            // 模拟移除角色
            Log::info("角色移除", [
                'user_id' => $userId,
                'role' => $role,
                'removed_at' => now()
            ]);
            
            // 清除用户权限缓存
            Cache::forget("user_permissions:{$userId}");
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色移除成功',
                'data' => [
                    'user_id' => $userId,
                    'role' => $role,
                    'removed_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'role' => $role,
            ];

            Log::error('角色移除失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色移除失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取所有角色列表
     */
    public function getRoles()
    {
        try {
            $roles = [
                [
                    'id' => 1,
                    'name' => 'admin',
                    'display_name' => '管理员',
                    'description' => '系统管理员，拥有所有权限',
                    'permissions_count' => count($this->defaultPermissions),
                    'users_count' => 1,
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 2,
                    'name' => 'editor',
                    'display_name' => '编辑者',
                    'description' => '内容编辑者，可以管理内容和用户',
                    'permissions_count' => 6,
                    'users_count' => 5,
                    'created_at' => now()->subDays(25)
                ],
                [
                    'id' => 3,
                    'name' => 'moderator',
                    'display_name' => '版主',
                    'description' => '版主，可以管理AI服务和查看用户',
                    'permissions_count' => 4,
                    'users_count' => 3,
                    'created_at' => now()->subDays(20)
                ],
                [
                    'id' => 4,
                    'name' => 'user',
                    'display_name' => '普通用户',
                    'description' => '普通用户，可以使用AI服务',
                    'permissions_count' => 4,
                    'users_count' => 100,
                    'created_at' => now()->subDays(15)
                ],
                [
                    'id' => 5,
                    'name' => 'guest',
                    'display_name' => '访客',
                    'description' => '访客用户，只能查看基本信息',
                    'permissions_count' => 2,
                    'users_count' => 50,
                    'created_at' => now()->subDays(10)
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色列表获取成功',
                'data' => [
                    'roles' => $roles,
                    'total' => count($roles)
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [];

            Log::error('角色列表获取失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色列表获取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取所有权限列表
     */
    public function getPermissions()
    {
        try {
            $permissions = [
                [
                    'id' => 1,
                    'name' => 'user.create',
                    'display_name' => '创建用户',
                    'description' => '创建新用户账户',
                    'category' => 'user',
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 2,
                    'name' => 'user.read',
                    'display_name' => '查看用户',
                    'description' => '查看用户信息',
                    'category' => 'user',
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 3,
                    'name' => 'user.update',
                    'display_name' => '更新用户',
                    'description' => '更新用户信息',
                    'category' => 'user',
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 4,
                    'name' => 'user.delete',
                    'display_name' => '删除用户',
                    'description' => '删除用户账户',
                    'category' => 'user',
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 5,
                    'name' => 'ai.generate',
                    'display_name' => 'AI生成',
                    'description' => '使用AI生成内容',
                    'category' => 'ai',
                    'created_at' => now()->subDays(25)
                ],
                [
                    'id' => 6,
                    'name' => 'ai.manage',
                    'display_name' => 'AI管理',
                    'description' => '管理AI服务和配置',
                    'category' => 'ai',
                    'created_at' => now()->subDays(25)
                ],
                [
                    'id' => 7,
                    'name' => 'ai.view',
                    'display_name' => '查看AI',
                    'description' => '查看AI生成结果',
                    'category' => 'ai',
                    'created_at' => now()->subDays(25)
                ],
                [
                    'id' => 8,
                    'name' => 'system.config',
                    'display_name' => '系统配置',
                    'description' => '修改系统配置',
                    'category' => 'system',
                    'created_at' => now()->subDays(20)
                ],
                [
                    'id' => 9,
                    'name' => 'system.monitor',
                    'display_name' => '系统监控',
                    'description' => '查看系统监控信息',
                    'category' => 'system',
                    'created_at' => now()->subDays(20)
                ],
                [
                    'id' => 10,
                    'name' => 'system.backup',
                    'display_name' => '系统备份',
                    'description' => '执行系统备份操作',
                    'category' => 'system',
                    'created_at' => now()->subDays(20)
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限列表获取成功',
                'data' => [
                    'permissions' => $permissions,
                    'total' => count($permissions),
                    'categories' => ['user', 'ai', 'system', 'role', 'permission']
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [];

            Log::error('权限列表获取失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限列表获取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 创建新角色
     */
    public function createRole($data)
    {
        try {
            $role = [
                'id' => rand(100, 999),
                'name' => $data['name'],
                'display_name' => $data['display_name'],
                'description' => $data['description'] ?? '',
                'permissions' => $data['permissions'] ?? [],
                'created_at' => now(),
                'updated_at' => now()
            ];
            
            // 验证角色数据
            $this->validateRole($role);
            
            Log::info("角色创建成功", $role);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色创建成功',
                'data' => $role
            ];
        } catch (\Exception $e) {
            $services_data = [
                'role_name' => $data['name'] ?? null,
                'role_type' => $data['type'] ?? null,
            ];

            Log::error('角色创建失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色创建失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 更新角色权限
     */
    public function updateRolePermissions($roleId, $permissions)
    {
        try {
            // 验证权限
            foreach ($permissions as $permission) {
                if (!in_array($permission, $this->defaultPermissions) && $permission !== '*') {
                    return [
                        'code' => ApiCodeEnum::INVALID_PARAMS,
                        'message' => '无效的权限：' . $permission,
                        'data' => null
                    ];
                }
            }
            
            Log::info("角色权限更新", [
                'role_id' => $roleId,
                'permissions' => $permissions,
                'updated_at' => now()
            ]);
            
            // 清除相关缓存
            Cache::flush(); // 简单粗暴地清除所有缓存
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色权限更新成功',
                'data' => [
                    'role_id' => $roleId,
                    'permissions' => $permissions,
                    'updated_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'roleId' => $roleId,
                'permissions_count' => is_array($permissions) ? count($permissions) : 0,
            ];

            Log::error('角色权限更新失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色权限更新失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取权限统计
     */
    public function getPermissionStats()
    {
        try {
            $stats = [
                'roles' => [
                    'total' => count($this->defaultRoles),
                    'active' => count($this->defaultRoles),
                    'custom' => 0
                ],
                'permissions' => [
                    'total' => count($this->defaultPermissions),
                    'by_category' => [
                        'user' => 4,
                        'ai' => 3,
                        'system' => 3,
                        'role' => 4,
                        'permission' => 4
                    ]
                ],
                'users' => [
                    'total' => 159,
                    'by_role' => [
                        'admin' => 1,
                        'editor' => 5,
                        'moderator' => 3,
                        'user' => 100,
                        'guest' => 50
                    ]
                ],
                'recent_activities' => [
                    [
                        'type' => 'role_assigned',
                        'user_id' => 123,
                        'role' => 'editor',
                        'timestamp' => now()->subMinutes(30)
                    ],
                    [
                        'type' => 'permission_checked',
                        'user_id' => 456,
                        'permission' => 'ai.generate',
                        'result' => 'granted',
                        'timestamp' => now()->subMinutes(15)
                    ],
                    [
                        'type' => 'role_created',
                        'role' => 'custom_role',
                        'timestamp' => now()->subHours(2)
                    ]
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限统计获取成功',
                'data' => [
                    'stats' => $stats,
                    'generated_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [];

            Log::error('权限统计获取失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限统计获取失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 检查资源访问权限
     */
    public function checkResourceAccess($userId, $resource, $action = 'read')
    {
        try {
            $permission = "{$resource}.{$action}";
            $result = $this->checkPermission($userId, $permission);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $result['data']['has_permission'] ? '资源访问权限验证通过' : '资源访问权限不足',
                'data' => [
                    'user_id' => $userId,
                    'resource' => $resource,
                    'action' => $action,
                    'permission' => $permission,
                    'has_access' => $result['data']['has_permission'],
                    'checked_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'resource' => $resource,
                'action' => $action,
            ];

            Log::error('资源访问权限检查失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '资源访问权限检查失败：',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取用户权限详情
     */
    public function getUserPermissionDetails($userId)
    {
        try {
            $userRoles = $this->getUserRoles($userId);
            $userPermissions = $this->getUserPermissions($userId);
            
            $roleDetails = [];
            foreach ($userRoles as $role) {
                $rolePermissions = $this->getRolePermissions($role);
                $roleDetails[] = [
                    'role' => $role,
                    'permissions' => $rolePermissions,
                    'permissions_count' => count($rolePermissions)
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '用户权限详情获取成功',
                'data' => [
                    'user_id' => $userId,
                    'roles' => $userRoles,
                    'role_details' => $roleDetails,
                    'all_permissions' => $userPermissions,
                    'permissions_count' => count($userPermissions),
                    'is_admin' => in_array('*', $userPermissions),
                    'retrieved_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
            ];

            Log::error('用户权限详情获取失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户权限详情获取失败：',
                'data' => null
            ];
        }
    }
    
    // 私有方法实现
    
    private function validateRole($role)
    {
        if (empty($role['name'])) {
            throw new \InvalidArgumentException("角色名称不能为空");
        }
        
        if (empty($role['display_name'])) {
            throw new \InvalidArgumentException("角色显示名称不能为空");
        }
        
        if (!preg_match('/^[a-z_]+$/', $role['name'])) {
            throw new \InvalidArgumentException("角色名称只能包含小写字母和下划线");
        }
        
        // 验证权限
        if (isset($role['permissions'])) {
            foreach ($role['permissions'] as $permission) {
                if (!in_array($permission, $this->defaultPermissions) && $permission !== '*') {
                    throw new \InvalidArgumentException("无效的权限：{$permission}");
                }
            }
        }
    }
    
    /**
     * 授予用户权限
     */
    public function grantPermissions($grantData)
    {
        try {
            $userId = $grantData['user_id'];
            $permissions = $grantData['permissions'];
            $expiresAt = $grantData['expires_at'] ?? null;
            $reason = $grantData['reason'] ?? '';
            $grantedBy = $grantData['granted_by'];

            $grantedPermissions = [];
            $failedPermissions = [];

            foreach ($permissions as $permission) {
                // 模拟权限授予逻辑
                if ($this->isValidPermission($permission)) {
                    $grantedPermissions[] = [
                        'permission' => $permission,
                        'expires_at' => $expiresAt
                    ];
                } else {
                    $failedPermissions[] = $permission;
                }
            }

            // 清除用户权限缓存
            $this->clearUserPermissionCache($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限授予成功',
                'data' => [
                    'user_id' => $userId,
                    'granted_permissions' => $grantedPermissions,
                    'failed_permissions' => $failedPermissions,
                    'granted_by' => $grantedBy,
                    'granted_at' => now(),
                    'reason' => $reason
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $grantData['user_id'] ?? null,
                'permissions_count' => is_array($grantData['permissions'] ?? []) ? count($grantData['permissions']) : 0,
                'expires_at' => $grantData['expires_at'] ?? null,
            ];

            Log::error('权限授予失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限授予失败：',
                'data' => null
            ];
        }
    }

    /**
     * 撤销用户权限
     */
    public function revokePermissions($revokeData)
    {
        try {
            $userId = $revokeData['user_id'];
            $permissions = $revokeData['permissions'];
            $reason = $revokeData['reason'] ?? '';
            $revokedBy = $revokeData['revoked_by'];

            $revokedPermissions = [];
            $failedPermissions = [];

            foreach ($permissions as $permission) {
                // 模拟权限撤销逻辑
                if ($this->userHasPermission($userId, $permission)) {
                    $revokedPermissions[] = $permission;
                } else {
                    $failedPermissions[] = $permission;
                }
            }

            // 清除用户权限缓存
            $this->clearUserPermissionCache($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限撤销成功',
                'data' => [
                    'user_id' => $userId,
                    'revoked_permissions' => $revokedPermissions,
                    'failed_permissions' => $failedPermissions,
                    'revoked_by' => $revokedBy,
                    'revoked_at' => now(),
                    'reason' => $reason
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $revokeData['user_id'] ?? null,
                'permissions_count' => is_array($revokeData['permissions'] ?? []) ? count($revokeData['permissions']) : 0,
                'reason' => $revokeData['reason'] ?? null,
            ];

            Log::error('权限撤销失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '权限撤销失败：',
                'data' => null
            ];
        }
    }

    /**
     * 获取权限历史
     */
    public function getPermissionHistory($filters)
    {
        try {
            $userId = $filters['user_id'];
            $action = $filters['action'] ?? null;
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;

            // 模拟权限历史数据
            $history = [
                [
                    'id' => 1,
                    'action' => 'grant',
                    'permission' => 'ai.video.generate',
                    'old_value' => null,
                    'new_value' => 'granted',
                    'expires_at' => '2024-12-31 23:59:59',
                    'operator' => 'admin',
                    'operator_id' => 1,
                    'reason' => '用户升级为高级会员',
                    'created_at' => '2024-01-01 12:00:00'
                ],
                [
                    'id' => 2,
                    'action' => 'assign_role',
                    'permission' => 'premium_user',
                    'old_value' => 'free_user',
                    'new_value' => 'premium_user',
                    'expires_at' => null,
                    'operator' => 'admin',
                    'operator_id' => 1,
                    'reason' => '角色升级',
                    'created_at' => '2024-01-01 11:30:00'
                ]
            ];

            // 根据action过滤
            if ($action) {
                $history = array_filter($history, function($item) use ($action) {
                    return $item['action'] === $action;
                });
            }

            $total = count($history);
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '权限历史获取成功',
                'data' => [
                    'user_id' => $userId,
                    'history' => array_values($history),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'filters' => $filters,
            ];

            Log::error('获取权限历史失败：', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取权限历史失败：',
                'data' => null
            ];
        }
    }

    /**
     * 检查权限是否有效
     */
    private function isValidPermission($permission)
    {
        $validPermissions = array_merge($this->defaultPermissions, ['*']);
        return in_array($permission, $validPermissions);
    }

    /**
     * 检查用户是否拥有指定权限
     */
    private function userHasPermission($userId, $permission)
    {
        $userPermissions = $this->getUserPermissions($userId);
        return in_array($permission, $userPermissions) || in_array('*', $userPermissions);
    }

    /**
     * 清除用户权限缓存
     */
    private function clearUserPermissionCache($userId)
    {
        Cache::forget("user_permissions:{$userId}");
        return true;
    }
}