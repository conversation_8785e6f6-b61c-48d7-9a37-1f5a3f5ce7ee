<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Carbon\Carbon;

/**
 * 通知服务类
 * 负责各种通知的发送、管理和统计
 */
class NotificationService extends Service
{
    private $channels = ['email', 'sms', 'push', 'webhook', 'slack', 'database'];
    private $priorities = ['low', 'normal', 'high', 'urgent'];
    private $types = ['system', 'user', 'marketing', 'security', 'alert'];
    
    /**
     * 发送通知
     */
    public function sendNotification($data)
    {
        try {
            $notification = [
                'id' => 'notif_' . uniqid(),
                'type' => $data['type'] ?? 'system',
                'channel' => $data['channel'] ?? 'email',
                'priority' => $data['priority'] ?? 'normal',
                'recipient' => $data['recipient'],
                'title' => $data['title'],
                'message' => $data['message'],
                'data' => $data['data'] ?? [],
                'scheduled_at' => $data['scheduled_at'] ?? now(),
                'created_at' => now(),
                'status' => 'pending'
            ];
            
            // 验证通知数据
            $this->validateNotification($notification);
            
            // 检查发送频率限制
            if (!$this->checkRateLimit($notification)) {
                return [
                    'code' => ApiCodeEnum::RATE_LIMIT_EXCEEDED,
                    'message' => '发送频率超限，请稍后再试',
                    'data' => null
                ];
            }
            
            // 立即发送或加入队列
            if ($notification['priority'] === 'urgent') {
                $result = $this->sendImmediately($notification);
            } else {
                $result = $this->queueNotification($notification);
            }
            
            // 记录发送日志
            $this->logNotification($notification, $result);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知发送成功',
                'data' => [
                    'notification_id' => $notification['id'],
                    'status' => $result['status'],
                    'sent_at' => $result['sent_at'] ?? null,
                    'queued' => $result['queued'] ?? false
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'type' => $data['type'] ?? null,
                'title' => $data['title'] ?? null,
                'recipient_id' => $data['recipient_id'] ?? null,
                'channel' => $data['channel'] ?? null,
            ];

            Log::error('通知发送失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知发送失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 批量发送通知
     */
    public function sendBulkNotifications($notifications)
    {
        try {
            $results = [];
            $successCount = 0;
            $failureCount = 0;
            
            foreach ($notifications as $index => $notificationData) {
                $result = $this->sendNotification($notificationData);
                $results[] = [
                    'index' => $index,
                    'recipient' => $notificationData['recipient'],
                    'result' => $result
                ];
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "批量通知发送完成，成功：{$successCount}，失败：{$failureCount}",
                'data' => [
                    'total' => count($notifications),
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'results' => $results
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'notifications_count' => is_array($notifications) ? count($notifications) : 0,
            ];

            Log::error('批量通知发送失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量通知发送失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取通知列表
     */
    public function getNotifications($params = [])
    {
        try {
            // 模拟通知数据（实际项目中应从数据库获取）
            $notifications = [
                [
                    'id' => 'notif_001',
                    'type' => 'system',
                    'channel' => 'email',
                    'priority' => 'high',
                    'recipient' => '<EMAIL>',
                    'title' => '系统维护通知',
                    'message' => '系统将于今晚22:00-24:00进行维护',
                    'status' => 'sent',
                    'sent_at' => now()->subHours(2),
                    'created_at' => now()->subHours(3)
                ],
                [
                    'id' => 'notif_002',
                    'type' => 'user',
                    'channel' => 'push',
                    'priority' => 'normal',
                    'recipient' => 'user123',
                    'title' => '任务完成通知',
                    'message' => '您的AI生成任务已完成',
                    'status' => 'delivered',
                    'sent_at' => now()->subMinutes(30),
                    'created_at' => now()->subMinutes(35)
                ],
                [
                    'id' => 'notif_003',
                    'type' => 'alert',
                    'channel' => 'slack',
                    'priority' => 'urgent',
                    'recipient' => '#alerts',
                    'title' => 'CPU使用率告警',
                    'message' => 'CPU使用率超过85%',
                    'status' => 'failed',
                    'error' => 'Slack webhook timeout',
                    'created_at' => now()->subMinutes(10)
                ]
            ];
            
            // 过滤通知
            $type = $params['type'] ?? null;
            $channel = $params['channel'] ?? null;
            $status = $params['status'] ?? null;
            $priority = $params['priority'] ?? null;
            
            if ($type) {
                $notifications = array_filter($notifications, fn($n) => $n['type'] === $type);
            }
            if ($channel) {
                $notifications = array_filter($notifications, fn($n) => $n['channel'] === $channel);
            }
            if ($status) {
                $notifications = array_filter($notifications, fn($n) => $n['status'] === $status);
            }
            if ($priority) {
                $notifications = array_filter($notifications, fn($n) => $n['priority'] === $priority);
            }
            
            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;
            $offset = ($page - 1) * $perPage;
            $paginatedNotifications = array_slice($notifications, $offset, $perPage);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知列表获取成功',
                'data' => [
                    'notifications' => array_values($paginatedNotifications),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => count($notifications),
                        'total_pages' => ceil(count($notifications) / $perPage)
                    ],
                    'filters' => [
                        'type' => $type,
                        'channel' => $channel,
                        'status' => $status,
                        'priority' => $priority
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'params' => $params,
            ];

            Log::error('通知列表获取失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知列表获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取通知详情
     */
    public function getNotification($notificationId)
    {
        try {
            // 模拟获取通知详情
            $notification = [
                'id' => $notificationId,
                'type' => 'system',
                'channel' => 'email',
                'priority' => 'high',
                'recipient' => '<EMAIL>',
                'title' => '系统维护通知',
                'message' => '系统将于今晚22:00-24:00进行维护，期间可能影响服务使用',
                'data' => [
                    'maintenance_start' => '2024-01-01 22:00:00',
                    'maintenance_end' => '2024-01-02 00:00:00',
                    'affected_services' => ['API', 'Web界面', '移动应用']
                ],
                'status' => 'sent',
                'sent_at' => now()->subHours(2),
                'delivered_at' => now()->subHours(2)->addMinutes(5),
                'read_at' => now()->subHours(1),
                'created_at' => now()->subHours(3),
                'attempts' => 1,
                'delivery_logs' => [
                    [
                        'timestamp' => now()->subHours(2),
                        'status' => 'sent',
                        'message' => '邮件发送成功'
                    ],
                    [
                        'timestamp' => now()->subHours(2)->addMinutes(5),
                        'status' => 'delivered',
                        'message' => '邮件已送达'
                    ]
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知详情获取成功',
                'data' => $notification
            ];
        } catch (\Exception $e) {
            $services_data = [
                'notification_id' => $notificationId,
            ];

            Log::error('通知详情获取失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知详情获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 重发通知
     */
    public function resendNotification($notificationId)
    {
        try {
            // 获取原通知信息
            $originalNotification = $this->getNotification($notificationId);
            
            if ($originalNotification['code'] !== ApiCodeEnum::SUCCESS) {
                return $originalNotification;
            }
            
            $notification = $originalNotification['data'];
            
            // 创建重发通知
            $resendData = [
                'type' => $notification['type'],
                'channel' => $notification['channel'],
                'priority' => $notification['priority'],
                'recipient' => $notification['recipient'],
                'title' => '[重发] ' . $notification['title'],
                'message' => $notification['message'],
                'data' => $notification['data']
            ];
            
            $result = $this->sendNotification($resendData);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知重发成功',
                'data' => [
                    'original_id' => $notificationId,
                    'resend_result' => $result
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'notification_id' => $notificationId,
            ];

            Log::error('通知重发失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知重发失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取通知统计
     */
    public function getNotificationStats($params = [])
    {
        try {
            $timeRange = $params['time_range'] ?? '24h';
            $groupBy = $params['group_by'] ?? 'hour';
            
            // 模拟统计数据
            $stats = [
                'summary' => [
                    'total_sent' => rand(1000, 5000),
                    'total_delivered' => rand(900, 4500),
                    'total_failed' => rand(50, 200),
                    'delivery_rate' => rand(85, 98) + rand(0, 99) / 100,
                    'average_delivery_time' => rand(5, 30) // 秒
                ],
                'by_channel' => [
                    'email' => [
                        'sent' => rand(500, 2000),
                        'delivered' => rand(450, 1800),
                        'failed' => rand(20, 100),
                        'delivery_rate' => rand(85, 95)
                    ],
                    'sms' => [
                        'sent' => rand(200, 800),
                        'delivered' => rand(180, 750),
                        'failed' => rand(10, 50),
                        'delivery_rate' => rand(90, 98)
                    ],
                    'push' => [
                        'sent' => rand(300, 1200),
                        'delivered' => rand(250, 1000),
                        'failed' => rand(20, 80),
                        'delivery_rate' => rand(80, 90)
                    ]
                ],
                'by_type' => [
                    'system' => rand(200, 800),
                    'user' => rand(500, 2000),
                    'marketing' => rand(100, 500),
                    'security' => rand(50, 200),
                    'alert' => rand(20, 100)
                ],
                'by_priority' => [
                    'low' => rand(100, 500),
                    'normal' => rand(500, 2000),
                    'high' => rand(200, 800),
                    'urgent' => rand(50, 200)
                ],
                'timeline' => $this->generateTimelineStats($timeRange, $groupBy)
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知统计获取成功',
                'data' => [
                    'time_range' => $timeRange,
                    'group_by' => $groupBy,
                    'generated_at' => now(),
                    'stats' => $stats
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'params' => $params,
            ];

            Log::error('通知统计获取失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知统计获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取通知模板
     */
    public function getTemplates($type = null)
    {
        try {
            $templates = [
                [
                    'id' => 'tpl_001',
                    'name' => '系统维护通知',
                    'type' => 'system',
                    'channel' => 'email',
                    'subject' => '系统维护通知',
                    'content' => '尊敬的用户，系统将于{{maintenance_time}}进行维护，预计耗时{{duration}}。',
                    'variables' => ['maintenance_time', 'duration'],
                    'created_at' => now()->subDays(30)
                ],
                [
                    'id' => 'tpl_002',
                    'name' => '任务完成通知',
                    'type' => 'user',
                    'channel' => 'push',
                    'subject' => '任务完成',
                    'content' => '您的{{task_type}}任务已完成，点击查看结果。',
                    'variables' => ['task_type', 'task_id'],
                    'created_at' => now()->subDays(15)
                ],
                [
                    'id' => 'tpl_003',
                    'name' => '安全告警',
                    'type' => 'security',
                    'channel' => 'sms',
                    'subject' => '安全告警',
                    'content' => '检测到异常登录，IP：{{ip_address}}，时间：{{login_time}}。',
                    'variables' => ['ip_address', 'login_time'],
                    'created_at' => now()->subDays(7)
                ]
            ];
            
            if ($type) {
                $templates = array_filter($templates, fn($t) => $t['type'] === $type);
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知模板获取成功',
                'data' => [
                    'templates' => array_values($templates),
                    'total' => count($templates),
                    'filter' => ['type' => $type]
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'type' => $type,
            ];

            Log::error('通知模板获取失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知模板获取失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 创建通知模板
     */
    public function createTemplate($data)
    {
        try {
            $template = [
                'id' => 'tpl_' . uniqid(),
                'name' => $data['name'],
                'type' => $data['type'],
                'channel' => $data['channel'],
                'subject' => $data['subject'],
                'content' => $data['content'],
                'variables' => $data['variables'] ?? [],
                'created_at' => now(),
                'updated_at' => now()
            ];
            
            // 验证模板
            $this->validateTemplate($template);
            
            Log::info("通知模板创建成功", $template);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知模板创建成功',
                'data' => $template
            ];
        } catch (\Exception $e) {
            $services_data = [
                'name' => $data['name'] ?? null,
                'type' => $data['type'] ?? null,
            ];

            Log::error('通知模板创建失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::TEMPLATE_ADD_FAILED,
                'message' => '通知模板创建失败',
                'data' => null
            ];
        }
    }
    
    /**
     * 获取通知配置
     */
    public function getNotificationConfig()
    {
        try {
            $config = [
                'channels' => [
                    'email' => [
                        'enabled' => true,
                        'driver' => 'smtp',
                        'rate_limit' => 100, // 每分钟
                        'retry_attempts' => 3,
                        'timeout' => 30
                    ],
                    'sms' => [
                        'enabled' => true,
                        'provider' => 'aliyun',
                        'rate_limit' => 50,
                        'retry_attempts' => 2,
                        'timeout' => 10
                    ],
                    'push' => [
                        'enabled' => true,
                        'provider' => 'firebase',
                        'rate_limit' => 200,
                        'retry_attempts' => 3,
                        'timeout' => 15
                    ],
                    'webhook' => [
                        'enabled' => true,
                        'rate_limit' => 500,
                        'retry_attempts' => 5,
                        'timeout' => 30
                    ]
                ],
                'priorities' => $this->priorities,
                'types' => $this->types,
                'queue_settings' => [
                    'default_queue' => 'notifications',
                    'high_priority_queue' => 'notifications-high',
                    'delay_seconds' => 0,
                    'max_attempts' => 3
                ],
                'rate_limits' => [
                    'per_user_per_minute' => 10,
                    'per_user_per_hour' => 100,
                    'global_per_minute' => 1000
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知配置获取成功',
                'data' => $config
            ];
        } catch (\Exception $e) {
            Log::error('通知配置获取失败', [
                'method' => __METHOD__,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知配置获取失败',
                'data' => null
            ];
        }
    }
    
    // 私有方法实现
    
    private function validateNotification($notification)
    {
        if (!in_array($notification['channel'], $this->channels)) {
            throw new \InvalidArgumentException("无效的通知渠道: {$notification['channel']}");
        }
        
        if (!in_array($notification['priority'], $this->priorities)) {
            throw new \InvalidArgumentException("无效的优先级: {$notification['priority']}");
        }
        
        if (!in_array($notification['type'], $this->types)) {
            throw new \InvalidArgumentException("无效的通知类型: {$notification['type']}");
        }
        
        if (empty($notification['recipient'])) {
            throw new \InvalidArgumentException("收件人不能为空");
        }
        
        if (empty($notification['title']) || empty($notification['message'])) {
            throw new \InvalidArgumentException("标题和消息不能为空");
        }
    }
    
    private function checkRateLimit($notification)
    {
        $key = "notification_rate_limit:{$notification['recipient']}";
        $count = Cache::get($key, 0);
        
        if ($count >= 10) { // 每分钟最多10条
            return false;
        }
        
        Cache::put($key, $count + 1, 60);
        return true;
    }
    
    private function sendImmediately($notification)
    {
        try {
            switch ($notification['channel']) {
                case 'email':
                    return $this->sendEmail($notification);
                case 'sms':
                    return $this->sendSMS($notification);
                case 'push':
                    return $this->sendPush($notification);
                case 'webhook':
                    return $this->sendWebhook($notification);
                case 'slack':
                    return $this->sendSlack($notification);
                default:
                    throw new \InvalidArgumentException("不支持的通知渠道: {$notification['channel']}");
            }
        } catch (\Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
                'sent_at' => now()
            ];
        }
    }
    
    private function queueNotification($notification)
    {
        // 模拟加入队列
        $queueName = $notification['priority'] === 'high' ? 'notifications-high' : 'notifications';
        
        return [
            'status' => 'queued',
            'queue' => $queueName,
            'queued_at' => now(),
            'queued' => true
        ];
    }
    
    private function sendEmail($notification)
    {
        // 模拟邮件发送
        sleep(1); // 模拟发送延迟
        
        return [
            'status' => 'sent',
            'sent_at' => now(),
            'message_id' => 'email_' . uniqid()
        ];
    }
    
    private function sendSMS($notification)
    {
        // 模拟短信发送
        return [
            'status' => 'sent',
            'sent_at' => now(),
            'message_id' => 'sms_' . uniqid()
        ];
    }
    
    private function sendPush($notification)
    {
        // 模拟推送发送
        return [
            'status' => 'sent',
            'sent_at' => now(),
            'message_id' => 'push_' . uniqid()
        ];
    }
    
    private function sendWebhook($notification)
    {
        // 模拟Webhook发送
        return [
            'status' => 'sent',
            'sent_at' => now(),
            'response_code' => 200
        ];
    }
    
    private function sendSlack($notification)
    {
        // 模拟Slack发送
        return [
            'status' => 'sent',
            'sent_at' => now(),
            'channel' => $notification['recipient']
        ];
    }
    
    private function logNotification($notification, $result)
    {
        Log::info("通知发送记录", [
            'notification_id' => $notification['id'],
            'type' => $notification['type'],
            'channel' => $notification['channel'],
            'recipient' => $notification['recipient'],
            'status' => $result['status'],
            'sent_at' => $result['sent_at'] ?? null
        ]);
    }
    
    private function generateTimelineStats($timeRange, $groupBy)
    {
        $timeline = [];
        $now = now();
        
        switch ($timeRange) {
            case '24h':
                for ($i = 23; $i >= 0; $i--) {
                    $time = $now->copy()->subHours($i);
                    $timeline[] = [
                        'time' => $time->format('H:00'),
                        'sent' => rand(50, 200),
                        'delivered' => rand(45, 180),
                        'failed' => rand(2, 20)
                    ];
                }
                break;
            case '7d':
                for ($i = 6; $i >= 0; $i--) {
                    $time = $now->copy()->subDays($i);
                    $timeline[] = [
                        'time' => $time->format('m-d'),
                        'sent' => rand(500, 2000),
                        'delivered' => rand(450, 1800),
                        'failed' => rand(20, 100)
                    ];
                }
                break;
            default:
                for ($i = 23; $i >= 0; $i--) {
                    $time = $now->copy()->subHours($i);
                    $timeline[] = [
                        'time' => $time->format('H:00'),
                        'sent' => rand(50, 200),
                        'delivered' => rand(45, 180),
                        'failed' => rand(2, 20)
                    ];
                }
        }
        
        return $timeline;
    }
    
    /**
     * 获取用户通知列表
     */
    public function getUserNotifications($userId, $filters)
    {
        try {
            // 模拟用户通知数据
            $notifications = [
                [
                    'id' => 1,
                    'user_id' => $userId,
                    'type' => 'task',
                    'title' => 'AI图像生成完成',
                    'content' => '您的图像生成任务已完成，点击查看结果',
                    'data' => [
                        'task_id' => 123,
                        'result_url' => 'https://api.tiptop.cn/files/123.jpg'
                    ],
                    'status' => 'unread',
                    'created_at' => '2024-01-01 12:00:00',
                    'read_at' => null
                ],
                [
                    'id' => 2,
                    'user_id' => $userId,
                    'type' => 'system',
                    'title' => '系统维护通知',
                    'content' => '系统将于今晚进行维护升级',
                    'data' => [],
                    'status' => 'read',
                    'created_at' => '2024-01-01 10:00:00',
                    'read_at' => '2024-01-01 10:30:00'
                ],
                [
                    'id' => 3,
                    'user_id' => $userId,
                    'type' => 'social',
                    'title' => '新的关注者',
                    'content' => '用户张三关注了您',
                    'data' => [
                        'follower_id' => 456,
                        'follower_name' => '张三'
                    ],
                    'status' => 'unread',
                    'created_at' => '2024-01-01 09:00:00',
                    'read_at' => null
                ]
            ];

            // 应用过滤器
            if ($filters['type']) {
                $notifications = array_filter($notifications, function($notification) use ($filters) {
                    return $notification['type'] === $filters['type'];
                });
            }

            if ($filters['status'] && $filters['status'] !== 'all') {
                $notifications = array_filter($notifications, function($notification) use ($filters) {
                    return $notification['status'] === $filters['status'];
                });
            }

            $total = count($notifications);
            $unreadCount = count(array_filter($notifications, function($n) {
                return $n['status'] === 'unread';
            }));

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;
            $offset = ($page - 1) * $perPage;
            $paginatedNotifications = array_slice($notifications, $offset, $perPage);
            $lastPage = ceil($total / $perPage);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '用户通知获取成功',
                'data' => [
                    'notifications' => array_values($paginatedNotifications),
                    'unread_count' => $unreadCount,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'filters' => $filters,
            ];

            Log::error('用户通知获取失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户通知获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 标记通知为已读
     */
    public function markAsRead($userId, $notificationIds)
    {
        try {
            $updatedCount = count($notificationIds);
            $remainingUnread = 2; // 模拟剩余未读数量

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知已标记为已读',
                'data' => [
                    'updated_count' => $updatedCount,
                    'remaining_unread' => $remainingUnread
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'notification_ids_count' => is_array($notificationIds) ? count($notificationIds) : 0,
            ];

            Log::error('标记已读失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '标记已读失败',
                'data' => null
            ];
        }
    }

    /**
     * 标记所有通知为已读
     */
    public function markAllAsRead($userId, $type = null)
    {
        try {
            $updatedCount = $type ? 5 : 15; // 模拟更新数量

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '所有通知已标记为已读',
                'data' => [
                    'updated_count' => $updatedCount
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
                'type' => $type,
            ];

            Log::error('批量标记已读失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量标记已读失败',
                'data' => null
            ];
        }
    }

    /**
     * 删除通知
     */
    public function deleteNotification($notificationId, $userId)
    {
        try {
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知删除成功',
                'data' => [
                    'deleted_id' => $notificationId
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'notification_id' => $notificationId,
                'user_id' => $userId,
            ];

            Log::error('通知删除失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '通知删除失败',
                'data' => null
            ];
        }
    }



    /**
     * 发送系统通知给多个用户（支持NotificationController调用）
     */
    public function sendSystemNotification($notificationData)
    {
        try {
            $userIds = $notificationData['user_ids'];
            $sentCount = empty($userIds) ? 150 : count($userIds); // 空数组表示发送给所有用户
            $notificationId = 'notif_' . uniqid();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '通知发送成功',
                'data' => [
                    'sent_count' => $sentCount,
                    'notification_id' => $notificationId
                ]
            ];
        } catch (\Exception $e) {
            $services_data = [
                'user_ids_count' => is_array($notificationData['user_ids'] ?? []) ? count($notificationData['user_ids']) : 0,
                'title' => $notificationData['title'] ?? null,
                'type' => $notificationData['type'] ?? null,
            ];

            Log::error('系统通知发送失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '系统通知发送失败',
                'data' => null
            ];
        }
    }

    private function validateTemplate($template)
    {
        if (empty($template['name'])) {
            throw new \InvalidArgumentException("模板名称不能为空");
        }
        
        if (!in_array($template['type'], $this->types)) {
            throw new \InvalidArgumentException("无效的模板类型: {$template['type']}");
        }
        
        if (!in_array($template['channel'], $this->channels)) {
            throw new \InvalidArgumentException("无效的通知渠道: {$template['channel']}");
        }
        
        if (empty($template['content'])) {
            throw new \InvalidArgumentException("模板内容不能为空");
        }
    }
}