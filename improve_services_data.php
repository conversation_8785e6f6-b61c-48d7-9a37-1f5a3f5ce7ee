<?php

/**
 * 改进 services_data 字段 - 根据方法参数智能生成合适的字段
 */

class ServicesDataImprover
{
    private $servicesPath;
    private $results = [];

    public function __construct($servicesPath = 'php/api/app/Services/Api')
    {
        $this->servicesPath = $servicesPath;
    }

    /**
     * 执行改进
     */
    public function improve()
    {
        echo "开始改进 services_data 字段...\n\n";
        
        // 获取所有服务文件
        $serviceFiles = $this->scanServiceFiles();
        
        foreach ($serviceFiles as $file) {
            $this->improveServiceFile($file);
        }

        $this->generateReport();
    }

    /**
     * 扫描所有服务文件
     */
    private function scanServiceFiles()
    {
        $files = [];
        $directory = new DirectoryIterator($this->servicesPath);
        
        foreach ($directory as $fileInfo) {
            if ($fileInfo->isDot()) continue;
            
            if ($fileInfo->isFile() && $fileInfo->getExtension() === 'php') {
                $files[] = $fileInfo->getPathname();
            }
        }
        
        sort($files);
        return $files;
    }

    /**
     * 改进单个服务文件
     */
    private function improveServiceFile($filePath)
    {
        $fileName = basename($filePath);
        echo "改进文件: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "  ❌ 无法读取文件\n";
            return;
        }

        $originalContent = $content;
        $improvedMethods = 0;

        // 查找所有包含占位符的 services_data
        $pattern = '/(\$services_data = \[\s*\/\/ 关键的非敏感字段\s*\];)/';
        
        $content = preg_replace_callback($pattern, function($matches) use (&$improvedMethods, $content) {
            // 查找这个 services_data 所在的方法
            $methodInfo = $this->findMethodContext($content, $matches[0]);
            
            if ($methodInfo) {
                $improvedMethods++;
                return $this->generateSmartServicesData($methodInfo);
            }
            
            return $matches[0]; // 无法改进，保持原样
        }, $content);

        if ($content !== $originalContent) {
            file_put_contents($filePath, $content);
            echo "  ✅ 改进了 {$improvedMethods} 个方法的 services_data\n";
        } else {
            echo "  ℹ️ 无需改进\n";
        }

        $this->results[$fileName] = $improvedMethods;
        echo "\n";
    }

    /**
     * 查找 services_data 所在的方法上下文
     */
    private function findMethodContext($content, $servicesDataCode)
    {
        // 查找 services_data 在内容中的位置
        $position = strpos($content, $servicesDataCode);
        if ($position === false) {
            return null;
        }

        // 向前查找最近的 public function
        $beforeContent = substr($content, 0, $position);
        
        // 查找最后一个 public function
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(([^)]*)\)/', $beforeContent, $matches, PREG_OFFSET_CAPTURE);
        
        if (empty($matches[1])) {
            return null;
        }

        // 获取最后一个匹配的方法
        $lastIndex = count($matches[1]) - 1;
        $methodName = $matches[1][$lastIndex][0];
        $parameters = $matches[2][$lastIndex][0];

        return [
            'name' => $methodName,
            'parameters' => $this->parseParameters($parameters)
        ];
    }

    /**
     * 解析方法参数
     */
    private function parseParameters($parametersString)
    {
        $parameters = [];
        
        if (trim($parametersString) === '') {
            return $parameters;
        }

        // 简单的参数解析
        $parts = explode(',', $parametersString);
        
        foreach ($parts as $part) {
            $part = trim($part);
            
            // 提取参数名（去掉类型提示和默认值）
            if (preg_match('/\$([a-zA-Z_][a-zA-Z0-9_]*)/', $part, $matches)) {
                $paramName = $matches[1];
                $parameters[] = [
                    'name' => $paramName,
                    'full' => $part
                ];
            }
        }

        return $parameters;
    }

    /**
     * 生成智能的 services_data
     */
    private function generateSmartServicesData($methodInfo)
    {
        $methodName = $methodInfo['name'];
        $parameters = $methodInfo['parameters'];

        $servicesDataFields = [];

        // 根据参数生成合适的字段
        foreach ($parameters as $param) {
            $paramName = $param['name'];
            
            // 跳过敏感参数
            if ($this->isSensitiveParameter($paramName)) {
                continue;
            }

            // 根据参数类型生成合适的记录方式
            if ($this->isArrayParameter($paramName)) {
                $servicesDataFields[] = "'{$paramName}_count' => is_array(\${$paramName}) ? count(\${$paramName}) : 0";
            } elseif ($this->isObjectParameter($paramName)) {
                $servicesDataFields[] = "'{$paramName}_type' => gettype(\${$paramName})";
            } else {
                $servicesDataFields[] = "'{$paramName}' => \${$paramName}";
            }
        }

        // 根据方法名添加特定字段
        $additionalFields = $this->getMethodSpecificFields($methodName);
        $servicesDataFields = array_merge($servicesDataFields, $additionalFields);

        // 生成最终的 services_data 代码
        if (empty($servicesDataFields)) {
            return '$services_data = [];';
        }

        $fieldsCode = implode(",\n            ", $servicesDataFields);
        
        return "\$services_data = [\n            {$fieldsCode},\n        ];";
    }

    /**
     * 检查是否为敏感参数
     */
    private function isSensitiveParameter($paramName)
    {
        $sensitiveParams = [
            'password', 'token', 'secret', 'key', 'auth', 'credential',
            'private', 'confidential', 'secure'
        ];

        $lowerParamName = strtolower($paramName);
        
        foreach ($sensitiveParams as $sensitive) {
            if (strpos($lowerParamName, $sensitive) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为数组参数
     */
    private function isArrayParameter($paramName)
    {
        $arrayIndicators = ['list', 'array', 'items', 'data', 'params', 'filters', 'options'];
        
        $lowerParamName = strtolower($paramName);
        
        foreach ($arrayIndicators as $indicator) {
            if (strpos($lowerParamName, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否为对象参数
     */
    private function isObjectParameter($paramName)
    {
        $objectIndicators = ['request', 'model', 'entity', 'object'];
        
        $lowerParamName = strtolower($paramName);
        
        foreach ($objectIndicators as $indicator) {
            if (strpos($lowerParamName, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取方法特定的字段
     */
    private function getMethodSpecificFields($methodName)
    {
        $fields = [];
        
        // 根据方法名模式添加特定字段
        if (strpos($methodName, 'get') === 0) {
            // get 方法通常需要记录查询条件
        } elseif (strpos($methodName, 'create') === 0) {
            // create 方法可能需要记录创建的资源类型
        } elseif (strpos($methodName, 'update') === 0) {
            // update 方法可能需要记录更新的字段
        } elseif (strpos($methodName, 'delete') === 0) {
            // delete 方法可能需要记录删除的资源
        }

        return $fields;
    }

    /**
     * 生成改进报告
     */
    private function generateReport()
    {
        echo "==================== services_data 改进报告 ====================\n\n";
        
        $totalFiles = count($this->results);
        $improvedFiles = count(array_filter($this->results));
        $totalImprovedMethods = array_sum($this->results);

        echo "📊 改进统计：\n";
        echo "- 总文件数：{$totalFiles}\n";
        echo "- 改进文件数：{$improvedFiles}\n";
        echo "- 总改进方法数：{$totalImprovedMethods}\n\n";

        if ($improvedFiles > 0) {
            echo "🔧 改进详情：\n";
            foreach ($this->results as $fileName => $improvedCount) {
                if ($improvedCount > 0) {
                    echo "- {$fileName}: 改进了 {$improvedCount} 个方法\n";
                }
            }
            echo "\n";
        }

        echo "✅ services_data 改进完成！\n";
        echo "\n📝 建议：\n";
        echo "- 请检查生成的 services_data 字段是否合适\n";
        echo "- 根据具体业务需求调整字段内容\n";
        echo "- 确保不记录敏感信息\n";
    }
}

// 执行改进
try {
    $improver = new ServicesDataImprover();
    $improver->improve();
} catch (Exception $e) {
    echo "❌ 改进过程中发生错误: " . $e->getMessage() . "\n";
}
