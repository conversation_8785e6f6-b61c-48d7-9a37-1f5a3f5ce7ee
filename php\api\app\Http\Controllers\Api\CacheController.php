<?php

namespace App\Http\Controllers\Api;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\CacheService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 缓存管理控制器
 * 处理缓存清理、缓存统计、缓存配置等
 */
class CacheController extends Controller
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * @ApiTitle(获取缓存统计)
     * @ApiSummary(查询系统缓存使用统计)
     * @ApiMethod(GET)
     * @ApiRoute(/api/cache/stats)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "redis": {
     *       "status": "connected",
     *       "memory_usage": "256MB",
     *       "memory_peak": "512MB",
     *       "memory_limit": "1GB",
     *       "usage_percentage": 25.6,
     *       "connected_clients": 15,
     *       "total_keys": 12450,
     *       "expired_keys": 3250,
     *       "evicted_keys": 125,
     *       "hit_rate": 89.5,
     *       "miss_rate": 10.5,
     *       "uptime": 86400
     *     },
     *     "application_cache": {
     *       "total_entries": 8520,
     *       "by_type": {
     *         "user_sessions": 1250,
     *         "api_responses": 3200,
     *         "ai_results": 2800,
     *         "file_metadata": 1270
     *       },
     *       "size_by_type": {
     *         "user_sessions": "15MB",
     *         "api_responses": "45MB",
     *         "ai_results": "180MB",
     *         "file_metadata": "8MB"
     *       },
     *       "hit_rate": 92.3,
     *       "avg_ttl": 3600
     *     },
     *     "performance": {
     *       "avg_get_time": 0.8,
     *       "avg_set_time": 1.2,
     *       "operations_per_second": 1250,
     *       "slow_queries": 5
     *     }
     *   }
     * })
     */
    public function getStats(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看缓存统计');
            }

            $result = $this->cacheService->getStats();

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存统计信息获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存统计信息获取失败');
        }
    }

    /**
     * @ApiTitle(清理缓存)
     * @ApiSummary(清理指定类型的缓存)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/cache/clear)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="缓存类型：all,sessions,api,ai,files,config")
     * @ApiParams(name="pattern", type="string", required=false, description="缓存键名模式")
     * @ApiParams(name="force", type="boolean", required=false, description="强制清理，默认false")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "缓存清理成功",
     *   "data": {
     *     "type": "api",
     *     "cleared_keys": 3200,
     *     "freed_memory": "45MB",
     *     "execution_time": 2.5,
     *     "affected_services": ["api_responses", "rate_limiting"],
     *     "cleared_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function clearCache(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可清理缓存');
            }

            $rules = [
                'type' => 'sometimes|string|in:all,sessions,api,ai,files,config,user_data',
                'pattern' => 'sometimes|string|max:200',
                'force' => 'sometimes|boolean'
            ];

            $this->validateData($request->all(), $rules);

            $clearData = [
                'type' => $request->get('type', 'all'),
                'pattern' => $request->get('pattern'),
                'force' => $request->get('force', false),
                'operator_id' => $user->id
            ];

            $result = $this->cacheService->clearCache($clearData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存清理失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存清理失败');
        }
    }

    /**
     * @ApiTitle(预热缓存)
     * @ApiSummary(预热指定类型的缓存数据)
     * @ApiMethod(POST)
     * @ApiRoute(/api/cache/warmup)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=true, description="预热类型：config,popular_content,user_preferences,ai_models")
     * @ApiParams(name="priority", type="string", required=false, description="优先级：low,normal,high")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "缓存预热任务创建成功",
     *   "data": {
     *     "warmup_id": "warmup_123456",
     *     "type": "popular_content",
     *     "status": "processing",
     *     "estimated_time": 300,
     *     "items_to_cache": 1250,
     *     "items_cached": 0,
     *     "progress": 0,
     *     "started_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function warmupCache(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可预热缓存');
            }

            $rules = [
                'type' => 'required|string|in:config,popular_content,user_preferences,ai_models,styles,characters',
                'priority' => 'sometimes|string|in:low,normal,high'
            ];

            $messages = [
                'type.required' => '预热类型不能为空',
                'type.in' => '不支持的预热类型'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $warmupData = [
                'type' => $request->type,
                'priority' => $request->get('priority', 'normal'),
                'initiated_by' => $user->id
            ];

            $result = $this->cacheService->warmupCache($warmupData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存预热任务创建失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存预热任务创建失败');
        }
    }

    /**
     * @ApiTitle(获取缓存键列表)
     * @ApiSummary(查询缓存中的键列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/cache/keys)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="pattern", type="string", required=false, description="键名模式，支持通配符")
     * @ApiParams(name="type", type="string", required=false, description="缓存类型筛选")
     * @ApiParams(name="limit", type="integer", required=false, description="返回数量限制，默认100")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "keys": [
     *       {
     *         "key": "user:session:123456",
     *         "type": "string",
     *         "size": "2KB",
     *         "ttl": 3600,
     *         "created_at": "2024-01-01 12:00:00",
     *         "last_accessed": "2024-01-01 12:30:00",
     *         "access_count": 15
     *       },
     *       {
     *         "key": "api:response:images:generate:hash123",
     *         "type": "hash",
     *         "size": "15KB",
     *         "ttl": 1800,
     *         "created_at": "2024-01-01 11:45:00",
     *         "last_accessed": "2024-01-01 12:25:00",
     *         "access_count": 8
     *       }
     *     ],
     *     "total_keys": 250,
     *     "total_size": "125MB",
     *     "pattern": "user:*",
     *     "scan_time": 0.05
     *   }
     * })
     */
    public function getKeys(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看缓存键');
            }

            $rules = [
                'pattern' => 'sometimes|string|max:200',
                'type' => 'sometimes|string|in:sessions,api,ai,files,config,user_data',
                'limit' => 'sometimes|integer|min:1|max:1000'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'pattern' => $request->get('pattern', '*'),
                'type' => $request->get('type'),
                'limit' => $request->get('limit', 100)
            ];

            $result = $this->cacheService->getKeys($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存键列表获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存键列表获取失败');
        }
    }

    /**
     * @ApiTitle(获取缓存值)
     * @ApiSummary(查询指定键的缓存值)
     * @ApiMethod(GET)
     * @ApiRoute(/api/cache/get)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="key", type="string", required=true, description="缓存键名")
     * @ApiParams(name="format", type="string", required=false, description="返回格式：raw,json,pretty")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "key": "user:session:123456",
     *     "value": {
     *       "user_id": 123,
     *       "username": "testuser",
     *       "login_time": "2024-01-01 12:00:00",
     *       "last_activity": "2024-01-01 12:30:00"
     *     },
     *     "metadata": {
     *       "type": "hash",
     *       "size": "2KB",
     *       "ttl": 3600,
     *       "created_at": "2024-01-01 12:00:00",
     *       "last_accessed": "2024-01-01 12:30:00",
     *       "access_count": 15
     *     }
     *   }
     * })
     */
    public function getValue(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看缓存值');
            }

            $rules = [
                'key' => 'required|string|max:500',
                'format' => 'sometimes|string|in:raw,json,pretty'
            ];

            $messages = [
                'key.required' => '缓存键名不能为空',
                'key.max' => '缓存键名不能超过500个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $key = $request->key;
            $format = $request->get('format', 'json');

            $result = $this->cacheService->getValue($key, $format);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存值获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存值获取失败');
        }
    }

    /**
     * @ApiTitle(设置缓存值)
     * @ApiSummary(设置指定键的缓存值)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/cache/set)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="key", type="string", required=true, description="缓存键名")
     * @ApiParams(name="value", type="mixed", required=true, description="缓存值")
     * @ApiParams(name="ttl", type="integer", required=false, description="过期时间(秒)")
     * @ApiParams(name="tags", type="array", required=false, description="缓存标签")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "缓存设置成功",
     *   "data": {
     *     "key": "custom:data:test",
     *     "size": "1KB",
     *     "ttl": 3600,
     *     "tags": ["test", "manual"],
     *     "set_at": "2024-01-01 12:00:00",
     *     "expires_at": "2024-01-01 13:00:00"
     *   }
     * })
     */
    public function setValue(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可设置缓存值');
            }

            $rules = [
                'key' => 'required|string|max:500',
                'value' => 'required',
                'ttl' => 'sometimes|integer|min:1|max:86400',
                'tags' => 'sometimes|array|max:10',
                'tags.*' => 'string|max:50'
            ];

            $messages = [
                'key.required' => '缓存键名不能为空',
                'key.max' => '缓存键名不能超过500个字符',
                'value.required' => '缓存值不能为空',
                'ttl.min' => 'TTL不能小于1秒',
                'ttl.max' => 'TTL不能超过24小时'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $cacheData = [
                'key' => $request->key,
                'value' => $request->value,
                'ttl' => $request->get('ttl', 3600),
                'tags' => $request->get('tags', []),
                'set_by' => $user->id
            ];

            $result = $this->cacheService->setValue($cacheData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存值设置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存值设置失败');
        }
    }

    /**
     * @ApiTitle(删除缓存键)
     * @ApiSummary(删除指定的缓存键)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/cache/delete)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="keys", type="array", required=true, description="要删除的缓存键数组")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "缓存键删除成功",
     *   "data": {
     *     "deleted_keys": [
     *       "user:session:123456",
     *       "api:response:test:hash123"
     *     ],
     *     "failed_keys": [],
     *     "deleted_count": 2,
     *     "freed_memory": "17KB",
     *     "deleted_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function deleteKeys(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可删除缓存键');
            }

            $rules = [
                'keys' => 'required|array|min:1|max:100',
                'keys.*' => 'required|string|max:500'
            ];

            $messages = [
                'keys.required' => '缓存键数组不能为空',
                'keys.array' => '缓存键必须是数组格式',
                'keys.min' => '至少需要1个缓存键',
                'keys.max' => '最多支持100个缓存键',
                'keys.*.required' => '缓存键不能为空',
                'keys.*.max' => '缓存键不能超过500个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $deleteData = [
                'keys' => $request->keys,
                'deleted_by' => $user->id
            ];

            $result = $this->cacheService->deleteKeys($deleteData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存键删除失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存键删除失败');
        }
    }

    /**
     * @ApiTitle(获取缓存配置)
     * @ApiSummary(查询缓存系统配置)
     * @ApiMethod(GET)
     * @ApiRoute(/api/cache/config)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "redis": {
     *       "host": "127.0.0.1",
     *       "port": 6379,
     *       "database": 0,
     *       "max_connections": 100,
     *       "timeout": 5,
     *       "retry_interval": 100
     *     },
     *     "application": {
     *       "default_ttl": 3600,
     *       "max_key_length": 500,
     *       "max_value_size": "10MB",
     *       "compression_enabled": true,
     *       "serialization": "json"
     *     },
     *     "policies": {
     *       "eviction_policy": "allkeys-lru",
     *       "max_memory": "1GB",
     *       "memory_samples": 5,
     *       "lazy_expire": true
     *     },
     *     "monitoring": {
     *       "slow_log_enabled": true,
     *       "slow_log_threshold": 10000,
     *       "stats_collection": true,
     *       "alert_thresholds": {
     *         "memory_usage": 80,
     *         "hit_rate": 85,
     *         "connection_count": 90
     *       }
     *     }
     *   }
     * })
     */
    public function getConfig(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看缓存配置');
            }

            $result = $this->cacheService->getConfig();

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('缓存配置获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '缓存配置获取失败');
        }
    }
}
