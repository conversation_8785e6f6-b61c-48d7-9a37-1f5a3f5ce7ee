<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Models\AiModelConfig;
use App\Models\PlatformPerformanceMetric;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Enums\ApiCodeEnum;

class AiPlatformHealthService extends Service
{
    // 🔧 修正：健康检查端点配置
    protected array $healthCheckEndpoints = [
        'deepseek' => '/deepseek/health',
        'liblib' => '/liblib/health',
        'kling' => '/kling/health',
        'minimax' => '/minimax/health',
        'volcengine' => '/volcengine/health'
    ];

    /**
     * 检查平台健康状态
     */
    public function checkPlatformHealth(string $platform): array
    {
        try {
            // 🔧 修正：优化缓存键命名
            $cacheKey = "ai_platform_health_{$platform}";
            
            return Cache::remember($cacheKey, 60, function () use ($platform) {
                $startTime = microtime(true);
                
                // 检查平台配置
                $config = config("ai.platforms.{$platform}");
                if (!$config) {
                    return [
                        'platform' => $platform,
                        'status' => 'unknown',
                        'response_time' => 0,
                        'error' => '平台配置不存在',
                        'last_check' => now()->toISOString()
                    ];
                }

                try {
                    // 执行健康检查
                    $endpoint = $this->healthCheckEndpoints[$platform] ?? '/health';
                    $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
                    $timeout = $config['timeout'] ?? 10;

                    $response = Http::timeout($timeout)->get($aiApiUrl . $endpoint);
                    $responseTime = (microtime(true) - $startTime) * 1000; // 转换为毫秒

                    if ($response->successful()) {
                        $responseData = $response->json();
                        
                        return [
                            'platform' => $platform,
                            'status' => 'healthy',
                            'response_time' => round($responseTime, 2),
                            'details' => $responseData,
                            'last_check' => now()->toISOString()
                        ];
                    } else {
                        return [
                            'platform' => $platform,
                            'status' => 'unhealthy',
                            'response_time' => round($responseTime, 2),
                            'error' => 'HTTP ' . $response->status(),
                            'last_check' => now()->toISOString()
                        ];
                    }

                } catch (\Exception $e) {
                    $responseTime = (microtime(true) - $startTime) * 1000;
                    
                    return [
                        'platform' => $platform,
                        'status' => 'unhealthy',
                        'response_time' => round($responseTime, 2),
                        'error' => $e->getMessage(),
                        'last_check' => now()->toISOString()
                    ];
                }
            });

        } catch (\Exception $e) {
            $services_data = [
                'platform' => $platform,
            ];

            Log::error('平台健康检查失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'platform' => $platform,
                'status' => 'unknown',
                'response_time' => 0,
                'error' => '健康检查失败: ' . $e->getMessage(),
                'last_check' => now()->toISOString()
            ];
        }
    }

    /**
     * 批量检查所有平台健康状态
     */
    public function checkAllPlatformsHealth(): array
    {
        try {
            // 原服务层的业务代码逻辑
            $platforms = array_keys($this->healthCheckEndpoints);
            $results = [];

            foreach ($platforms as $platform) {
                $results[$platform] = $this->checkPlatformHealth($platform);
            }

            return [
                'timestamp' => now()->toISOString(),
                'total_platforms' => count($platforms),
                'healthy_count' => count(array_filter($results, fn($r) => $r['status'] === 'healthy')),
                'platforms' => $results
            ];

        } catch (\Exception $e) {
            $services_data = [];

            Log::error('批量检查平台健康状态失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量检查平台健康状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取平台可用性统计 
     */
    public function getPlatformAvailability(string $platform, int $days = 7): array
    {
        try {
            $metrics = PlatformPerformanceMetric::where('platform', $platform)
                ->where('metric_date', '>=', now()->subDays($days))
                ->orderBy('metric_date', 'desc')
                ->get();

            if ($metrics->isEmpty()) {
                return [
                    'platform' => $platform,
                    'period_days' => $days,
                    'availability' => 0,
                    'avg_response_time' => 0,
                    'success_rate' => 0,
                    'total_requests' => 0,
                    'message' => '暂无统计数据'
                ];
            }

            $totalRequests = $metrics->sum('total_requests');
            $failedRequests = $metrics->sum('failed_requests');
            $avgResponseTime = $metrics->avg('response_time_avg');
            $avgUptime = $metrics->avg('uptime_percentage');

            $successRate = $totalRequests > 0 
                ? (($totalRequests - $failedRequests) / $totalRequests) * 100 
                : 0;

            return [
                'platform' => $platform,
                'period_days' => $days,
                'availability' => round($avgUptime, 2),
                'avg_response_time' => round($avgResponseTime, 2),
                'success_rate' => round($successRate, 2),
                'total_requests' => $totalRequests,
                'failed_requests' => $failedRequests,
                'data_points' => $metrics->count()
            ];

        } catch (\Exception $e) {
            $services_data = [
                'platform' => $platform,
                'days' => $days,
            ];

            Log::error('获取平台可用性统计失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'platform' => $platform,
                'period_days' => $days,
                'availability' => 0,
                'error' => '获取统计数据失败'
            ];
        }
    }

    /**
     * 记录平台性能指标
     */
    public function recordPerformanceMetric(
        string $platform,
        string $businessType,
        float $responseTime,
        bool $success,
        float $cost = 0.0
    ): void {
        try {
            $today = now()->toDateString();
            
            // 🔧 修正：使用firstOrNew避免重复创建
            $metric = PlatformPerformanceMetric::firstOrNew([
                'platform' => $platform,
                'business_type' => $businessType,
                'metric_date' => $today
            ]);

            // 更新指标
            $metric->total_requests = ($metric->total_requests ?? 0) + 1;
            if (!$success) {
                $metric->failed_requests = ($metric->failed_requests ?? 0) + 1;
            }

            // 计算平均响应时间
            $totalRequests = $metric->total_requests;
            $currentAvg = $metric->response_time_avg ?? 0;
            $metric->response_time_avg = (($currentAvg * ($totalRequests - 1)) + $responseTime) / $totalRequests;

            // 计算成功率
            $metric->success_rate = (($totalRequests - $metric->failed_requests) / $totalRequests) * 100;

            // 更新成本评分（简化计算）
            $metric->cost_score = max(0, min(10, 10 - ($cost * 100)));

            // 🔧 新增：更新质量评分（基于成功率和响应时间）
            $responseTimeScore = max(0, min(10, 10 - ($responseTime / 10)));
            $successRateScore = $metric->success_rate / 10;
            $metric->quality_score = ($responseTimeScore + $successRateScore) / 2;

            // 🔧 新增：更新可用性百分比
            if ($success) {
                $metric->uptime_percentage = min(100, ($metric->uptime_percentage ?? 100));
            } else {
                $failureImpact = 100 / $totalRequests; // 每次失败的影响
                $metric->uptime_percentage = max(0, ($metric->uptime_percentage ?? 100) - $failureImpact);
            }

            $metric->save();

            // 🔧 新增：更新缓存中的性能数据
            $this->updatePerformanceCache($platform, $businessType, $metric);

        } catch (\Exception $e) {
            $services_data = [
                'platform' => $platform,
                'business_type' => $businessType,
                'response_time' => $responseTime,
                'success' => $success,
                'cost' => $cost,
            ];

            Log::error('记录AI平台性能指标失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🔧 新增：更新性能缓存
     */
    protected function updatePerformanceCache(string $platform, string $businessType, PlatformPerformanceMetric $metric): void
    {
        $cacheKey = "ai_platform_performance_{$businessType}";
        $performanceData = Cache::get($cacheKey, []);
        
        $performanceData[$platform] = [
            'avg_response_time' => $metric->response_time_avg,
            'avg_success_rate' => $metric->success_rate,
            'avg_cost_score' => $metric->cost_score,
            'avg_quality_score' => $metric->quality_score,
            'avg_uptime' => $metric->uptime_percentage,
            'last_updated' => now()->toISOString()
        ];
        
        Cache::put($cacheKey, $performanceData, 300);
    }

    /**
     * 🔧 新增：获取平台健康状态摘要
     */
    public function getHealthSummary(): array
    {
        try {
            // 原服务层的业务代码逻辑
            $platforms = array_keys($this->healthCheckEndpoints);
            $summary = [
                'total_platforms' => count($platforms),
                'healthy_platforms' => 0,
                'unhealthy_platforms' => 0,
                'unknown_platforms' => 0,
                'platform_details' => [],
                'overall_health' => 'unknown',
                'last_check' => now()->toISOString()
            ];

            foreach ($platforms as $platform) {
                $health = $this->checkPlatformHealth($platform);
                $summary['platform_details'][$platform] = $health;

                switch ($health['status']) {
                    case 'healthy':
                        $summary['healthy_platforms']++;
                        break;
                    case 'unhealthy':
                        $summary['unhealthy_platforms']++;
                        break;
                    default:
                        $summary['unknown_platforms']++;
                        break;
                }
            }

            // 计算整体健康状态
            $healthyRatio = $summary['healthy_platforms'] / $summary['total_platforms'];
            if ($healthyRatio >= 0.8) {
                $summary['overall_health'] = 'healthy';
            } elseif ($healthyRatio >= 0.5) {
                $summary['overall_health'] = 'degraded';
            } else {
                $summary['overall_health'] = 'unhealthy';
            }

            return $summary;

        } catch (\Exception $e) {
            $services_data = [];

            Log::error('获取平台健康状态摘要失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取平台健康状态摘要失败',
                'data' => null
            ];
        }
    }

    /**
     * 🔧 新增：检查平台是否需要告警
     */
    public function checkAlertConditions(string $platform): array
    {
        try {
            // 原服务层的业务代码逻辑
            $health = $this->checkPlatformHealth($platform);
            $availability = $this->getPlatformAvailability($platform, 1); // 最近1天

            $alerts = [];

            // 健康状态告警
            if ($health['status'] === 'unhealthy') {
                $alerts[] = [
                    'type' => 'health_critical',
                    'message' => "平台 {$platform} 健康检查失败",
                    'details' => $health,
                    'severity' => 'critical'
                ];
            }

            // 响应时间告警
            if ($health['response_time'] > 5000) { // 超过5秒
                $alerts[] = [
                    'type' => 'response_time_high',
                    'message' => "平台 {$platform} 响应时间过长: {$health['response_time']}ms",
                    'details' => ['response_time' => $health['response_time']],
                    'severity' => 'warning'
                ];
            }

            // 成功率告警
            if ($availability['success_rate'] < 90) { // 成功率低于90%
                $alerts[] = [
                    'type' => 'success_rate_low',
                    'message' => "平台 {$platform} 成功率过低: {$availability['success_rate']}%",
                    'details' => $availability,
                    'severity' => 'warning'
                ];
            }

            // 可用性告警
            if ($availability['availability'] < 95) { // 可用性低于95%
                $alerts[] = [
                    'type' => 'availability_low',
                    'message' => "平台 {$platform} 可用性过低: {$availability['availability']}%",
                    'details' => $availability,
                    'severity' => 'critical'
                ];
            }

            return $alerts;

        } catch (\Exception $e) {
            $services_data = [
                'platform' => $platform,
            ];

            Log::error('检查平台告警条件失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '检查平台告警条件失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取平台性能对比数据 - 里程碑2接口1实施
     *
     * @param array $filters 筛选条件
     * @return array
     */
    public function getPlatformComparison(array $filters = []): array
    {
        try {
            // 1. 获取可用平台列表
            $platforms = $this->getAvailablePlatforms($filters);

            if (empty($platforms)) {
                return [
                    'code' => 404,
                    'message' => '无可用平台数据',
                    'data' => []
                ];
            }

            // 2. 获取性能指标数据
            $performanceData = $this->getPerformanceMetrics($platforms);

            // 3. 计算对比指标
            $comparisonData = $this->calculateComparisonMetrics($performanceData, $filters);

            // 4. 生成推荐建议
            $recommendations = $this->generateRecommendations($comparisonData);

            return [
                'code' => 200,
                'message' => '平台对比数据获取成功',
                'data' => [
                    'platforms' => $comparisonData,
                    'recommendations' => $recommendations,
                    'last_updated' => date('c')
                ]
            ];

        } catch (\Exception $e) {
            $services_data = [
                'filters' => $filters,
            ];

            Log::error('获取平台对比数据失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取平台对比数据失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取可用平台列表 - 基于实际集成的AI平台
     */
    private function getAvailablePlatforms(array $filters): array
    {
        // 实际集成的AI平台映射
        $platformMapping = [
            'image' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'video' => ['KlingAI', 'MiniMax'],
            'story' => ['DeepSeek', 'MiniMax'],
            'character' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'style' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'sound' => ['火山引擎豆包', 'MiniMax'],
            'voice' => ['MiniMax', '火山引擎豆包'],
            'music' => ['MiniMax']
        ];

        // 模拟平台数据（实际项目中应从数据库获取）
        $allPlatforms = [
            ['id' => 1, 'name' => 'LiblibAI', 'business_types' => ['image', 'character', 'style'], 'status' => 'active'],
            ['id' => 2, 'name' => 'KlingAI', 'business_types' => ['image', 'video', 'character', 'style'], 'status' => 'active'],
            ['id' => 3, 'name' => 'MiniMax', 'business_types' => ['image', 'video', 'story', 'character', 'style', 'sound', 'voice', 'music'], 'status' => 'active'],
            ['id' => 4, 'name' => 'DeepSeek', 'business_types' => ['story'], 'status' => 'active'],
            ['id' => 5, 'name' => '火山引擎豆包', 'business_types' => ['sound', 'voice'], 'status' => 'active']
        ];

        // 按业务类型筛选
        if (!empty($filters['business_type'])) {
            $businessType = $filters['business_type'];
            $allPlatforms = array_filter($allPlatforms, function($platform) use ($businessType) {
                return in_array($businessType, $platform['business_types']);
            });
        }

        return array_values($allPlatforms);
    }

    /**
     * 获取性能指标数据
     */
    private function getPerformanceMetrics(array $platforms): array
    {
        $metrics = [];

        // 模拟性能数据（实际项目中应从监控系统获取）
        $performanceData = [
            'LiblibAI' => ['response_time' => 2.8, 'success_rate' => 96.5, 'cost_per_request' => 0.03, 'quality_score' => 8.8],
            'KlingAI' => ['response_time' => 3.5, 'success_rate' => 94.2, 'cost_per_request' => 0.05, 'quality_score' => 8.5],
            'MiniMax' => ['response_time' => 2.2, 'success_rate' => 98.1, 'cost_per_request' => 0.04, 'quality_score' => 9.1],
            'DeepSeek' => ['response_time' => 1.8, 'success_rate' => 97.8, 'cost_per_request' => 0.02, 'quality_score' => 8.9],
            '火山引擎豆包' => ['response_time' => 2.5, 'success_rate' => 95.3, 'cost_per_request' => 0.035, 'quality_score' => 8.6]
        ];

        foreach ($platforms as $platform) {
            $platformName = $platform['name'];
            if (isset($performanceData[$platformName])) {
                $metrics[$platform['id']] = array_merge($platform, $performanceData[$platformName]);
            }
        }

        return $metrics;
    }

    /**
     * 计算对比指标并排序
     */
    private function calculateComparisonMetrics(array $performanceData, array $filters): array
    {
        $sortBy = $filters['sort_by'] ?? 'response_time';
        $order = $filters['order'] ?? 'asc';

        // 转换为数组格式
        $platforms = array_values($performanceData);

        // 排序
        usort($platforms, function($a, $b) use ($sortBy, $order) {
            $valueA = $a[$sortBy] ?? 0;
            $valueB = $b[$sortBy] ?? 0;

            if ($order === 'desc') {
                return $valueB <=> $valueA;
            }
            return $valueA <=> $valueB;
        });

        return $platforms;
    }

    /**
     * 生成推荐建议
     */
    private function generateRecommendations(array $platforms): array
    {
        if (empty($platforms)) {
            return [];
        }

        $recommendations = [];

        // 找出最佳平台
        $bestQuality = null;
        $fastestResponse = null;
        $highestSuccess = null;
        $mostCostEffective = null;

        foreach ($platforms as $platform) {
            // 最高质量
            if (!$bestQuality || $platform['quality_score'] > $bestQuality['quality_score']) {
                $bestQuality = $platform;
            }

            // 最快响应
            if (!$fastestResponse || $platform['response_time'] < $fastestResponse['response_time']) {
                $fastestResponse = $platform;
            }

            // 最高成功率
            if (!$highestSuccess || $platform['success_rate'] > $highestSuccess['success_rate']) {
                $highestSuccess = $platform;
            }

            // 最具成本效益
            if (!$mostCostEffective || $platform['cost_per_request'] < $mostCostEffective['cost_per_request']) {
                $mostCostEffective = $platform;
            }
        }

        // 生成推荐
        if ($bestQuality) {
            $recommendations[] = [
                'platform_id' => $bestQuality['id'],
                'reason' => '最高质量评分，适合高质量内容生成',
                'score' => $bestQuality['quality_score']
            ];
        }

        return array_slice($recommendations, 0, 3); // 最多返回3个推荐
    }

    /**
     * 根据业务类型获取平台列表 - 里程碑2接口2实施
     *
     * @param string $businessType 业务类型
     * @return array
     */
    public function getBusinessPlatforms(string $businessType): array
    {
        try {
            // 1. 验证业务类型
            if (!$this->isValidBusinessType($businessType)) {
                return [
                    'code' => 422,
                    'message' => '业务类型无效',
                    'data' => []
                ];
            }

            // 2. 获取支持该业务类型的平台
            $platforms = $this->getPlatformsByBusinessType($businessType);

            if (empty($platforms)) {
                return [
                    'code' => 404,
                    'message' => '暂无支持该业务类型的平台',
                    'data' => []
                ];
            }

            return [
                'code' => 200,
                'message' => '获取平台列表成功',
                'data' => [
                    'business_type' => $businessType,
                    'platforms' => $platforms,
                    'total_count' => count($platforms)
                ]
            ];

        } catch (\Exception $e) {
            $services_data = [
                'business_type' => $businessType,
            ];

            Log::error('获取业务平台列表失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取业务平台列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 验证业务类型有效性
     */
    private function isValidBusinessType(string $businessType): bool
    {
        $validTypes = ['image', 'video', 'story', 'character', 'style', 'sound', 'voice', 'music'];
        return in_array($businessType, $validTypes);
    }

    /**
     * 根据业务类型获取平台
     */
    private function getPlatformsByBusinessType(string $businessType): array
    {
        // 实际集成的AI平台映射
        $platformMapping = [
            'image' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'video' => ['KlingAI', 'MiniMax'],
            'story' => ['DeepSeek', 'MiniMax'],
            'character' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'style' => ['LiblibAI', 'KlingAI', 'MiniMax'],
            'sound' => ['火山引擎豆包', 'MiniMax'],
            'voice' => ['MiniMax', '火山引擎豆包'],
            'music' => ['MiniMax']
        ];

        $platformNames = $platformMapping[$businessType] ?? [];
        $platforms = [];

        // 模拟平台ID映射
        $platformIds = [
            'LiblibAI' => 1,
            'KlingAI' => 2,
            'MiniMax' => 3,
            'DeepSeek' => 4,
            '火山引擎豆包' => 5
        ];

        foreach ($platformNames as $name) {
            $platforms[] = [
                'id' => $platformIds[$name] ?? 0,
                'name' => $name,
                'business_type' => $businessType,
                'status' => 'active'
            ];
        }

        return $platforms;
    }
}
