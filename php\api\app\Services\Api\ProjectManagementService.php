<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\Project;
use App\Models\ProjectCollaborator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 项目管理功能服务
 * 专注于项目管理、协作、统计分析等高级功能
 * 不包含基础的项目CRUD操作（由ProjectService负责）
 */
class ProjectManagementService extends Service
{
    /**
     * 创建项目管理任务
     */
    public function createTask(int $userId, array $taskData): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $project = Project::where('id', $taskData['project_id'])
                ->where(function ($query) use ($userId) {
                    $query->where('user_id', $userId)
                          ->orWhereHas('collaborators', function ($q) use ($userId) {
                              $q->where('user_id', $userId)
                                ->where('status', ProjectCollaborator::STATUS_ACTIVE);
                          });
                })
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限',
                    'data' => []
                ];
            }

            // 创建任务（这里需要创建ProjectTask模型，暂时使用数组模拟）
            $taskId = time() + rand(1000, 9999); // 模拟任务ID

            $task = [
                'task_id' => $taskId,
                'project_id' => $taskData['project_id'],
                'task_name' => $taskData['task_name'],
                'description' => $taskData['description'] ?? '',
                'priority' => $taskData['priority'] ?? 'medium',
                'assigned_to' => $taskData['assigned_to'] ?? null,
                'due_date' => $taskData['due_date'] ?? null,
                'status' => 'pending',
                'created_by' => $userId,
                'created_at' => Carbon::now()->format('Y-m-d H:i:s')
            ];

            // 这里应该保存到数据库，暂时模拟
            // ProjectTask::create($task);

            DB::commit();

            Log::info('项目管理任务创建成功', [
                'task_id' => $taskId,
                'project_id' => $taskData['project_id'],
                'user_id' => $userId,
                'task_name' => $taskData['task_name']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '项目管理任务创建成功',
                'data' => $task
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $services_data = [
                'user_id' => $userId,
                'task_type' => $taskData['type'] ?? null,
                'project_id' => $taskData['project_id'] ?? null,
                'title' => $taskData['title'] ?? null,
            ];

            Log::error('项目管理任务创建失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '项目管理任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 管理协作
     */
    public function manageCollaboration(int $projectId, int $userId, array $collaborationData): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限管理',
                    'data' => []
                ];
            }

            $targetUserId = $collaborationData['user_id'];
            $action = $collaborationData['action'];

            switch ($action) {
                case 'invite':
                    $result = $this->inviteCollaborator($project, $targetUserId, $collaborationData);
                    break;
                case 'remove':
                    $result = $this->removeCollaborator($project, $targetUserId);
                    break;
                case 'update_role':
                    $result = $this->updateCollaboratorRole($project, $targetUserId, $collaborationData);
                    break;
                default:
                    return [
                        'code' => ApiCodeEnum::INVALID_PARAMETER,
                        'message' => '无效的操作类型',
                        'data' => []
                    ];
            }

            DB::commit();

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('协作管理失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'action' => $collaborationData['action'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '协作管理失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取项目进度
     */
    public function getProjectProgress(int $projectId, int $userId): array
    {
        try {
            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where(function ($query) use ($userId) {
                    $query->where('user_id', $userId)
                          ->orWhereHas('collaborators', function ($q) use ($userId) {
                              $q->where('user_id', $userId)
                                ->where('status', ProjectCollaborator::STATUS_ACTIVE);
                          });
                })
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限访问',
                    'data' => []
                ];
            }

            // 模拟项目进度数据（实际应该从数据库获取）
            $progressData = [
                'project_id' => $projectId,
                'overall_progress' => 65.5,
                'tasks' => [
                    'total' => 20,
                    'completed' => 13,
                    'in_progress' => 5,
                    'pending' => 2
                ],
                'milestones' => [
                    'total' => 5,
                    'completed' => 3,
                    'upcoming' => 2
                ],
                'timeline' => [
                    'start_date' => '2024-01-01',
                    'end_date' => '2024-03-01',
                    'current_phase' => 'development',
                    'days_remaining' => 45
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $progressData
            ];

        } catch (\Exception $e) {
            Log::error('获取项目进度失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目进度失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 分配项目资源
     */
    public function assignResources(int $projectId, int $userId, string $resourceType, array $resourceData): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where(function ($query) use ($userId) {
                    $query->where('user_id', $userId)
                          ->orWhereHas('collaborators', function ($q) use ($userId) {
                              $q->where('user_id', $userId)
                                ->where('status', ProjectCollaborator::STATUS_ACTIVE);
                          });
                })
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限',
                    'data' => []
                ];
            }

            // 模拟资源分配逻辑
            $allocatedResources = [];
            $totalCost = 0;

            switch ($resourceType) {
                case 'human':
                    foreach ($resourceData as $resource) {
                        $allocatedResources[] = [
                            'resource_id' => $resource['user_id'] ?? rand(100, 999),
                            'resource_name' => $resource['name'] ?? '开发者',
                            'allocation_percentage' => $resource['percentage'] ?? 50,
                            'start_date' => $resource['start_date'] ?? Carbon::now()->format('Y-m-d'),
                            'end_date' => $resource['end_date'] ?? Carbon::now()->addMonth()->format('Y-m-d')
                        ];
                        $totalCost += $resource['cost'] ?? 5000;
                    }
                    break;
                case 'equipment':
                    foreach ($resourceData as $resource) {
                        $allocatedResources[] = [
                            'resource_id' => rand(1000, 9999),
                            'resource_name' => $resource['name'] ?? '设备',
                            'quantity' => $resource['quantity'] ?? 1,
                            'start_date' => $resource['start_date'] ?? Carbon::now()->format('Y-m-d'),
                            'end_date' => $resource['end_date'] ?? Carbon::now()->addMonth()->format('Y-m-d')
                        ];
                        $totalCost += $resource['cost'] ?? 2000;
                    }
                    break;
                case 'budget':
                    $totalCost = $resourceData['amount'] ?? 10000;
                    $allocatedResources[] = [
                        'resource_id' => rand(10000, 99999),
                        'resource_name' => '项目预算',
                        'amount' => $totalCost,
                        'allocated_date' => Carbon::now()->format('Y-m-d')
                    ];
                    break;
            }

            DB::commit();

            Log::info('项目资源分配成功', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'resource_type' => $resourceType,
                'total_cost' => $totalCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '资源分配成功',
                'data' => [
                    'project_id' => $projectId,
                    'resource_type' => $resourceType,
                    'allocated_resources' => $allocatedResources,
                    'total_cost' => $totalCost,
                    'allocated_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('项目资源分配失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '资源分配失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取项目统计
     */
    public function getProjectStatistics(int $userId, ?int $projectId = null): array
    {
        try {
            if ($projectId) {
                // 获取特定项目的统计
                $project = Project::where('id', $projectId)
                    ->where(function ($query) use ($userId) {
                        $query->where('user_id', $userId)
                              ->orWhereHas('collaborators', function ($q) use ($userId) {
                                  $q->where('user_id', $userId)
                                    ->where('status', ProjectCollaborator::STATUS_ACTIVE);
                              });
                    })
                    ->first();

                if (!$project) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '项目不存在或无权限访问',
                        'data' => []
                    ];
                }

                // 单个项目统计
                $statistics = [
                    'project_statistics' => [
                        'project_id' => $projectId,
                        'total_tasks' => 15,
                        'completed_tasks' => 10,
                        'in_progress_tasks' => 3,
                        'overdue_tasks' => 2
                    ],
                    'resource_statistics' => [
                        'allocated_budget' => 25000,
                        'used_budget' => 15000,
                        'team_members' => 5,
                        'active_collaborations' => 3
                    ]
                ];
            } else {
                // 获取用户所有项目的统计
                $statistics = [
                    'project_statistics' => [
                        'total_projects' => 15,
                        'active_projects' => 8,
                        'completed_projects' => 5,
                        'archived_projects' => 2
                    ],
                    'task_statistics' => [
                        'total_tasks' => 120,
                        'completed_tasks' => 85,
                        'in_progress_tasks' => 25,
                        'overdue_tasks' => 10
                    ],
                    'resource_statistics' => [
                        'total_budget' => 50000,
                        'used_budget' => 32000,
                        'team_members' => 12,
                        'active_collaborations' => 8
                    ]
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $statistics
            ];

        } catch (\Exception $e) {
            Log::error('获取项目统计失败', [
                'user_id' => $userId,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目统计失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取项目里程碑
     */
    public function getProjectMilestones(int $projectId, int $userId): array
    {
        try {
            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where(function ($query) use ($userId) {
                    $query->where('user_id', $userId)
                          ->orWhereHas('collaborators', function ($q) use ($userId) {
                              $q->where('user_id', $userId)
                                ->where('status', ProjectCollaborator::STATUS_ACTIVE);
                          });
                })
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权限访问',
                    'data' => []
                ];
            }

            // 模拟里程碑数据（实际应该从数据库获取）
            $milestones = [
                [
                    'milestone_id' => 1,
                    'title' => '项目启动',
                    'description' => '项目正式启动',
                    'status' => 'completed',
                    'due_date' => '2024-01-01',
                    'completed_date' => '2024-01-01',
                    'progress' => 100
                ],
                [
                    'milestone_id' => 2,
                    'title' => '第一阶段完成',
                    'description' => '完成第一阶段开发',
                    'status' => 'in_progress',
                    'due_date' => '2024-02-01',
                    'completed_date' => null,
                    'progress' => 75
                ],
                [
                    'milestone_id' => 3,
                    'title' => '测试阶段',
                    'description' => '完成系统测试',
                    'status' => 'pending',
                    'due_date' => '2024-02-15',
                    'completed_date' => null,
                    'progress' => 0
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'project_id' => $projectId,
                    'milestones' => $milestones
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取项目里程碑失败', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目里程碑失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function inviteCollaborator(Project $project, int $userId, array $data): array
    {
        // 检查是否已经是协作者
        $existing = ProjectCollaborator::where('project_id', $project->id)
            ->where('user_id', $userId)
            ->first();

        if ($existing) {
            return [
                'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                'message' => '用户已经是项目协作者',
                'data' => []
            ];
        }

        $collaborator = ProjectCollaborator::create([
            'project_id' => $project->id,
            'user_id' => $userId,
            'role' => $data['role'],
            'permissions' => $data['permissions'],
            'status' => ProjectCollaborator::STATUS_INVITED,
            'invited_by' => $project->user_id
        ]);

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '协作管理操作成功',
            'data' => [
                'project_id' => $project->id,
                'action' => 'invite',
                'user_id' => $userId,
                'role' => $collaborator->role,
                'permissions' => $collaborator->permissions,
                'invite_status' => 'sent',
                'updated_at' => $collaborator->created_at->format('Y-m-d H:i:s')
            ]
        ];
    }

    private function removeCollaborator(Project $project, int $userId): array
    {
        $collaborator = ProjectCollaborator::where('project_id', $project->id)
            ->where('user_id', $userId)
            ->first();

        if (!$collaborator) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '协作者不存在',
                'data' => []
            ];
        }

        $collaborator->delete();

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '协作管理操作成功',
            'data' => [
                'project_id' => $project->id,
                'action' => 'remove',
                'user_id' => $userId,
                'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        ];
    }

    private function updateCollaboratorRole(Project $project, int $userId, array $data): array
    {
        $collaborator = ProjectCollaborator::where('project_id', $project->id)
            ->where('user_id', $userId)
            ->first();

        if (!$collaborator) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '协作者不存在',
                'data' => []
            ];
        }

        $collaborator->update([
            'role' => $data['role'],
            'permissions' => $data['permissions']
        ]);

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => '协作管理操作成功',
            'data' => [
                'project_id' => $project->id,
                'action' => 'update_role',
                'user_id' => $userId,
                'role' => $collaborator->role,
                'permissions' => $collaborator->permissions,
                'updated_at' => $collaborator->updated_at->format('Y-m-d H:i:s')
            ]
        ];
    }


}
