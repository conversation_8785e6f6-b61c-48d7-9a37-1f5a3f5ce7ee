<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\AudioService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 音频处理与格式转换
 */
class AudioController extends Controller
{
    protected $audioService;

    public function __construct(AudioService $audioService)
    {
        $this->audioService = $audioService;
    }

    /**
     * @ApiTitle(音频混音)
     * @ApiSummary(混合多个音频文件)
     * @ApiMethod(POST)
     * @ApiRoute(/api/audio/mix)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="audio_urls", type="array", required=true, description="音频URL数组")
     * @ApiParams(name="mix_config", type="object", required=false, description="混音配置")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "音频混音任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.1000"
     *   }
     * })
     */
    public function mix(Request $request)
    {
        try {
            $rules = [
                'audio_urls' => 'required|array|min:2|max:10',
                'audio_urls.*' => 'required|url',
                'mix_config' => 'sometimes|array',
                'mix_config.output_format' => 'sometimes|string|in:mp3,wav,flac',
                'mix_config.bitrate' => 'sometimes|string|in:128kbps,192kbps,320kbps',
                'mix_config.normalize' => 'sometimes|boolean',
                'project_id' => 'sometimes|integer|exists:projects,id'
            ];

            $messages = [
                'audio_urls.required' => '音频URL数组不能为空',
                'audio_urls.min' => '至少需要2个音频文件',
                'audio_urls.max' => '最多支持10个音频文件',
                'audio_urls.*.required' => '音频URL不能为空',
                'audio_urls.*.url' => '音频URL格式不正确',
                'mix_config.output_format.in' => '输出格式必须是：mp3、wav、flac之一',
                'mix_config.bitrate.in' => '比特率必须是：128kbps、192kbps、320kbps之一',
                'project_id.exists' => '项目不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            
            $mixConfig = array_merge([
                'output_format' => 'mp3',
                'bitrate' => '320kbps',
                'normalize' => true
            ], $request->get('mix_config', []));

            $result = $this->audioService->mixAudio(
                $user->id,
                $request->audio_urls,
                $request->project_id,
                $mixConfig
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('音频混音失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '音频混音失败');
        }
    }

    /**
     * @ApiTitle(音频混音状态查询)
     * @ApiSummary(查询音频混音任务的状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/audio/mix/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "audio_mix",
     *     "status": "completed",
     *     "audio_url": "https://aiapi.tiptop.cn/audio/mixed/123.mp3",
     *     "duration": 120,
     *     "file_size": "5.2MB",
     *     "cost": "0.1000",
     *     "processing_time_ms": 30000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:30"
     *   }
     * })
     */
    public function getMixStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->audioService->getAudioMixStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取音频混合状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取音频混合状态失败');
        }
    }

    /**
     * @ApiTitle(音频增强)
     * @ApiSummary(增强音频质量)
     * @ApiMethod(POST)
     * @ApiRoute(/api/audio/enhance)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="audio_url", type="string", required=true, description="音频URL")
     * @ApiParams(name="enhance_config", type="object", required=false, description="增强配置")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "音频增强任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.0800"
     *   }
     * })
     */
    public function enhance(Request $request)
    {
        try {
            $rules = [
                'audio_url' => 'required|url',
                'enhance_config' => 'sometimes|array',
                'enhance_config.noise_reduction' => 'sometimes|boolean',
                'enhance_config.volume_normalize' => 'sometimes|boolean',
                'enhance_config.bass_boost' => 'sometimes|numeric|min:0|max:10',
                'enhance_config.treble_boost' => 'sometimes|numeric|min:0|max:10',
                'project_id' => 'sometimes|integer|exists:projects,id'
            ];

            $messages = [
                'audio_url.required' => '音频URL不能为空',
                'audio_url.url' => '音频URL格式不正确',
                'enhance_config.bass_boost.min' => '低音增强不能小于0',
                'enhance_config.bass_boost.max' => '低音增强不能大于10',
                'enhance_config.treble_boost.min' => '高音增强不能小于0',
                'enhance_config.treble_boost.max' => '高音增强不能大于10',
                'project_id.exists' => '项目不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $enhanceConfig = array_merge([
                'noise_reduction' => true,
                'volume_normalize' => true,
                'bass_boost' => 0,
                'treble_boost' => 0
            ], $request->get('enhance_config', []));

            $result = $this->audioService->enhanceAudio(
                $user->id,
                $request->audio_url,
                $request->project_id,
                $enhanceConfig
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('音频增强失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '音频增强失败');
        }
    }

    /**
     * @ApiTitle(音频增强状态查询)
     * @ApiSummary(查询音频增强任务的状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/audio/enhance/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "audio_enhance",
     *     "status": "completed",
     *     "audio_url": "https://aiapi.tiptop.cn/audio/enhanced/123.mp3",
     *     "duration": 60,
     *     "file_size": "2.8MB",
     *     "cost": "0.0800",
     *     "processing_time_ms": 20000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:20"
     *   }
     * })
     */
    public function getEnhanceStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->audioService->getAudioEnhanceStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取音频增强状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取音频增强状态失败');
        }
    }
}
