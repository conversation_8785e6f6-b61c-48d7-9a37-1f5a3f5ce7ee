<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\User;
use App\Models\UserLevel;
use App\Models\Achievement;
use App\Models\UserAchievement;
use App\Models\DailyTask;
use App\Models\UserDailyTask;
use App\Models\GrowthHistory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 用户成长服务
 * 第5阶段：社交功能扩展
 */
class UserGrowthService extends Service
{
    /**
     * 获取用户成长档案
     */
    public function getUserGrowthProfile(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            // 获取用户等级信息
            $levelInfo = $this->getUserLevelInfo($userId);
            
            // 获取用户徽章
            $badges = $this->getUserBadges($userId);
            
            // 获取用户成就
            $achievements = $this->getUserAchievements($userId);
            
            // 获取用户统计
            $statistics = $this->getUserStatistics($userId);

            $data = [
                'user_id' => $userId,
                'level' => $levelInfo['level'],
                'experience' => $levelInfo['experience'],
                'experience_to_next_level' => $levelInfo['experience_to_next_level'],
                'total_experience_for_next_level' => $levelInfo['total_experience_for_next_level'],
                'level_progress' => $levelInfo['level_progress'],
                'title' => $levelInfo['title'],
                'badges' => $badges,
                'achievements' => $achievements,
                'statistics' => $statistics
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $services_data = [
                'user_id' => $userId,
            ];

            Log::error('获取用户成长档案失败', [
                'method' => __METHOD__,
                'services_data' => $services_data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取成长档案失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取排行榜
     */
    public function getLeaderboard(int $userId, array $params): array
    {
        try {
            $cacheKey = 'leaderboard_' . $params['type'] . '_' . $params['period'];
            
            return Cache::remember($cacheKey, 300, function () use ($userId, $params) {
                $query = User::query();

                // 根据类型排序
                switch ($params['type']) {
                    case 'level':
                        $query->orderBy('level', 'desc')->orderBy('experience', 'desc');
                        break;
                    case 'experience':
                        $query->orderBy('experience', 'desc');
                        break;
                    case 'creations':
                        $query->withCount('resources')->orderBy('resources_count', 'desc');
                        break;
                    case 'likes':
                        $query->orderBy('total_likes', 'desc');
                        break;
                    case 'followers':
                        $query->orderBy('follower_count', 'desc');
                        break;
                }

                // 时间周期过滤（简化实现）
                if ($params['period'] !== 'all') {
                    // 这里可以根据具体需求实现时间过滤
                }

                $users = $query->limit($params['limit'])->get();

                $rankings = [];
                foreach ($users as $index => $user) {
                    $value = $this->getUserRankingValue($user, $params['type']);
                    
                    $rankings[] = [
                        'rank' => $index + 1,
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'avatar' => $user->avatar,
                        'level' => $user->level ?? 1,
                        'experience' => $user->experience ?? 0,
                        'value' => $value,
                        'badge' => $this->getUserTopBadge($user->id)
                    ];
                }

                // 获取当前用户排名
                $myRank = $this->getUserRank($userId, $params['type']);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'leaderboard_type' => $params['type'],
                        'period' => $params['period'],
                        'my_rank' => $myRank,
                        'rankings' => $rankings,
                        'total_participants' => User::count(),
                        'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                    ]
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取排行榜失败', [
                'user_id' => $userId,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取排行榜失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 完成成就
     */
    public function completeAchievement(int $userId, array $achievementData): array
    {
        try {
            DB::beginTransaction();

            $achievement = Achievement::find($achievementData['achievement_id']);
            if (!$achievement) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '成就不存在',
                    'data' => []
                ];
            }

            // 检查是否已完成
            $userAchievement = UserAchievement::where('user_id', $userId)
                ->where('achievement_id', $achievement->id)
                ->first();

            if ($userAchievement && $userAchievement->completed) {
                return [
                    'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                    'message' => '成就已完成',
                    'data' => []
                ];
            }

            // 创建或更新用户成就记录
            if (!$userAchievement) {
                $userAchievement = UserAchievement::create([
                    'user_id' => $userId,
                    'achievement_id' => $achievement->id,
                    'progress' => $achievement->target,
                    'completed' => true,
                    'completed_at' => Carbon::now()
                ]);
            } else {
                $userAchievement->update([
                    'progress' => $achievement->target,
                    'completed' => true,
                    'completed_at' => Carbon::now()
                ]);
            }

            // 奖励经验值
            $user = User::find($userId);
            $oldLevel = $user->level ?? 1;
            $this->addExperience($userId, $achievement->reward_experience);
            
            // 检查是否升级
            $user->refresh();
            $newLevel = $user->level ?? 1;
            $levelUp = $newLevel > $oldLevel;

            // 记录成长历史
            GrowthHistory::create([
                'user_id' => $userId,
                'type' => 'achievement',
                'title' => '成就完成',
                'description' => "完成成就：{$achievement->name}",
                'data' => [
                    'achievement_id' => $achievement->id,
                    'achievement_name' => $achievement->name,
                    'experience_gained' => $achievement->reward_experience
                ]
            ]);

            DB::commit();

            Log::info('成就完成', [
                'user_id' => $userId,
                'achievement_id' => $achievement->id,
                'experience_gained' => $achievement->reward_experience
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '成就完成',
                'data' => [
                    'achievement_id' => $achievement->id,
                    'name' => $achievement->name,
                    'description' => $achievement->description,
                    'reward_experience' => $achievement->reward_experience,
                    'reward_badge' => $achievement->reward_badge ? [
                        'badge_id' => $achievement->reward_badge['id'],
                        'name' => $achievement->reward_badge['name'],
                        'icon' => $achievement->reward_badge['icon']
                    ] : null,
                    'completed_at' => $userAchievement->completed_at->format('Y-m-d H:i:s'),
                    'level_up' => $levelUp,
                    'new_level' => $newLevel,
                    'total_experience' => $user->experience ?? 0
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('完成成就失败', [
                'user_id' => $userId,
                'achievement_id' => $achievementData['achievement_id'],
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '完成成就失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取每日任务
     */
    public function getDailyTasks(int $userId): array
    {
        try {
            $today = Carbon::now()->format('Y-m-d');
            
            // 获取今日任务
            $tasks = DailyTask::where('date', $today)
                ->orWhere('date', null) // 常规任务
                ->get();

            $taskList = [];
            foreach ($tasks as $task) {
                $userTask = UserDailyTask::where('user_id', $userId)
                    ->where('task_id', $task->id)
                    ->where('date', $today)
                    ->first();

                $taskList[] = [
                    'task_id' => $task->id,
                    'name' => $task->name,
                    'description' => $task->description,
                    'type' => $task->type,
                    'target' => $task->target,
                    'progress' => $userTask ? $userTask->progress : 0,
                    'completed' => $userTask ? $userTask->completed : false,
                    'reward_experience' => $task->reward_experience,
                    'expires_at' => Carbon::now()->addDay()->startOfDay()->format('Y-m-d H:i:s')
                ];
            }

            // 计算完成统计
            $completedTasks = collect($taskList)->where('completed', true)->count();
            $totalTasks = count($taskList);
            $completionRate = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;

            // 获取连续天数
            $streakDays = $this->getUserTaskStreak($userId);

            // 今日获得经验
            $todayExperience = UserDailyTask::where('user_id', $userId)
                ->where('date', $today)
                ->where('completed', true)
                ->join('daily_tasks', 'user_daily_tasks.task_id', '=', 'daily_tasks.id')
                ->sum('daily_tasks.reward_experience');

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'date' => $today,
                    'tasks' => $taskList,
                    'completion_stats' => [
                        'completed_tasks' => $completedTasks,
                        'total_tasks' => $totalTasks,
                        'completion_rate' => round($completionRate, 1),
                        'streak_days' => $streakDays,
                        'total_experience_today' => $todayExperience
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取每日任务失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取每日任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 完成每日任务
     */
    public function completeDailyTask(int $userId, array $taskData): array
    {
        try {
            DB::beginTransaction();

            $task = DailyTask::find($taskData['task_id']);
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $today = Carbon::now()->format('Y-m-d');
            
            // 获取或创建用户任务记录
            $userTask = UserDailyTask::firstOrCreate([
                'user_id' => $userId,
                'task_id' => $task->id,
                'date' => $today
            ], [
                'progress' => 0,
                'completed' => false
            ]);

            if ($userTask->completed) {
                return [
                    'code' => ApiCodeEnum::DUPLICATE_OPERATION,
                    'message' => '任务已完成',
                    'data' => []
                ];
            }

            // 更新任务进度
            $userTask->update([
                'progress' => $task->target,
                'completed' => true,
                'completed_at' => Carbon::now()
            ]);

            // 奖励经验值
            $user = User::find($userId);
            $oldLevel = $user->level ?? 1;
            
            $experienceGained = $task->reward_experience;
            
            // 检查连续奖励
            $streakDays = $this->getUserTaskStreak($userId);
            $bonusReward = null;
            if ($streakDays >= 7) {
                $bonusExperience = 10;
                $experienceGained += $bonusExperience;
                $bonusReward = [
                    'type' => 'streak_bonus',
                    'experience' => $bonusExperience,
                    'description' => '连续完成任务奖励'
                ];
            }

            $this->addExperience($userId, $experienceGained);
            
            // 检查是否升级
            $user->refresh();
            $newLevel = $user->level ?? 1;
            $levelUp = $newLevel > $oldLevel;

            DB::commit();

            Log::info('每日任务完成', [
                'user_id' => $userId,
                'task_id' => $task->id,
                'experience_gained' => $experienceGained
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '每日任务完成',
                'data' => [
                    'task_id' => $task->id,
                    'name' => $task->name,
                    'reward_experience' => $task->reward_experience,
                    'completed_at' => $userTask->completed_at->format('Y-m-d H:i:s'),
                    'bonus_reward' => $bonusReward,
                    'level_up' => $levelUp,
                    'new_level' => $newLevel,
                    'total_experience' => $user->experience ?? 0
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('完成每日任务失败', [
                'user_id' => $userId,
                'task_id' => $taskData['task_id'],
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '完成每日任务失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取成长历史
     */
    public function getGrowthHistory(int $userId, array $filters): array
    {
        try {
            $query = GrowthHistory::where('user_id', $userId);

            // 应用过滤条件
            if (!empty($filters['type'])) {
                $query->where('type', $filters['type']);
            }

            if (!empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $history = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $historyList = [];
            foreach ($history->items() as $item) {
                $historyList[] = [
                    'id' => $item->id,
                    'type' => $item->type,
                    'title' => $item->title,
                    'description' => $item->description,
                    'data' => $item->data,
                    'created_at' => $item->created_at->format('Y-m-d H:i:s')
                ];
            }

            // 计算统计信息
            $statistics = $this->calculateGrowthStatistics($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'history' => $historyList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $history->currentPage(),
                        'per_page' => $history->perPage(),
                        'total' => $history->total(),
                        'last_page' => $history->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取成长历史失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取成长历史失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取成长统计
     */
    public function getGrowthStatistics(int $userId, array $params): array
    {
        try {
            $period = $params['period'];
            $startDate = $this->getStartDateByPeriod($period);

            // 获取期间内的成长数据
            $experienceGained = GrowthHistory::where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->where('type', 'level_up')
                ->sum('data->experience_gained') ?? 0;

            $levelsGained = GrowthHistory::where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->where('type', 'level_up')
                ->count();

            $achievementsCompleted = GrowthHistory::where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->where('type', 'achievement')
                ->count();

            $badgesEarned = GrowthHistory::where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->where('type', 'badge')
                ->count();

            $dailyTasksCompleted = UserDailyTask::where('user_id', $userId)
                ->where('completed', true)
                ->where('created_at', '>=', $startDate)
                ->count();

            // 获取趋势数据（简化实现）
            $growthTrends = $this->getGrowthTrends($userId, $startDate);
            $activityHeatmap = $this->getActivityHeatmap($userId, $startDate);

            // 对比数据（简化实现）
            $comparison = [
                'previous_period' => [
                    'experience_gained' => $experienceGained * 0.77, // 模拟数据
                    'growth_rate' => 30.8
                ],
                'average_user' => [
                    'experience_gained' => 420,
                    'percentile' => 85
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'period' => $period,
                    'experience_gained' => $experienceGained,
                    'levels_gained' => $levelsGained,
                    'achievements_completed' => $achievementsCompleted,
                    'badges_earned' => $badgesEarned,
                    'daily_tasks_completed' => $dailyTasksCompleted,
                    'creation_count' => 15, // 从其他地方获取
                    'interaction_count' => 89, // 从其他地方获取
                    'growth_trends' => $growthTrends,
                    'activity_heatmap' => $activityHeatmap,
                    'comparison' => $comparison
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取成长统计失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取成长统计失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 设置用户目标
     */
    public function setUserGoals(int $userId, array $goals): array
    {
        try {
            // 简化实现，实际项目中需要专门的目标表
            $user = User::find($userId);
            $user->update(['goals' => $goals]);

            $goalList = [];
            foreach ($goals as $index => $goal) {
                $current = $this->getCurrentValueForGoal($userId, $goal['type']);
                $progress = $goal['target'] > 0 ? ($current / $goal['target']) * 100 : 0;
                
                $goalList[] = [
                    'goal_id' => $index + 1,
                    'type' => $goal['type'],
                    'target' => $goal['target'],
                    'current' => $current,
                    'deadline' => $goal['deadline'],
                    'progress' => min(100, round($progress, 1))
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '成长目标设置成功',
                'data' => [
                    'goals' => $goalList,
                    'total_goals' => count($goals),
                    'achievable_goals' => count(array_filter($goalList, function($g) { return $g['progress'] >= 50; })),
                    'estimated_completion' => '2024-01-25'
                ]
            ];

        } catch (\Exception $e) {
            Log::error('设置用户目标失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '设置目标失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取成长建议
     */
    public function getGrowthRecommendations(int $userId): array
    {
        try {
            $user = User::find($userId);
            
            // 分析用户当前状态
            $recommendations = [];
            $focusAreas = [];
            $nextMilestones = [];

            // 检查接近完成的成就
            $nearCompletionAchievements = UserAchievement::where('user_id', $userId)
                ->where('completed', false)
                ->where('progress', '>', 0)
                ->with('achievement')
                ->get();

            foreach ($nearCompletionAchievements as $userAchievement) {
                $remaining = $userAchievement->achievement->target - $userAchievement->progress;
                if ($remaining <= 3) {
                    $recommendations[] = [
                        'type' => 'achievement',
                        'title' => "完成\"{$userAchievement->achievement->name}\"成就",
                        'description' => "还需要{$remaining}次即可完成此成就",
                        'priority' => 'high',
                        'reward' => "{$userAchievement->achievement->reward_experience}经验值",
                        'action' => '继续相关活动'
                    ];
                }
            }

            // 分析重点改进领域
            $focusAreas[] = [
                'area' => '创作频率',
                'current_score' => 7,
                'target_score' => 9,
                'suggestions' => ['设置每日创作提醒', '参与创作挑战']
            ];

            // 下一个里程碑
            $currentLevel = $user->level ?? 1;
            $nextLevel = $currentLevel + 1;
            $experienceNeeded = $this->getExperienceForLevel($nextLevel) - ($user->experience ?? 0);
            
            $nextMilestones[] = [
                'milestone' => "升级到{$nextLevel}级",
                'progress' => 95.0,
                'estimated_time' => '2天',
                'required_actions' => ['完成2个每日任务']
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendations' => $recommendations,
                    'focus_areas' => $focusAreas,
                    'next_milestones' => $nextMilestones
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取成长建议失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取成长建议失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function getUserLevelInfo(int $userId): array
    {
        $user = User::find($userId);
        $level = $user->level ?? 1;
        $experience = $user->experience ?? 0;
        
        $currentLevelExp = $this->getExperienceForLevel($level);
        $nextLevelExp = $this->getExperienceForLevel($level + 1);
        
        $experienceToNext = $nextLevelExp - $experience;
        $levelProgress = (($experience - $currentLevelExp) / ($nextLevelExp - $currentLevelExp)) * 100;
        
        return [
            'level' => $level,
            'experience' => $experience,
            'experience_to_next_level' => $experienceToNext,
            'total_experience_for_next_level' => $nextLevelExp,
            'level_progress' => round($levelProgress, 1),
            'title' => $this->getLevelTitle($level)
        ];
    }

    private function getUserBadges(int $userId): array
    {
        // 简化实现
        return [
            [
                'badge_id' => 1,
                'name' => '故事新手',
                'description' => '创作第一个故事',
                'icon' => 'https://example.com/badge1.png',
                'earned_at' => '2024-01-01 12:00:00'
            ]
        ];
    }

    private function getUserAchievements(int $userId): array
    {
        $achievements = UserAchievement::where('user_id', $userId)
            ->with('achievement')
            ->get();

        $achievementList = [];
        foreach ($achievements as $userAchievement) {
            $achievementList[] = [
                'achievement_id' => $userAchievement->achievement->id,
                'name' => $userAchievement->achievement->name,
                'description' => $userAchievement->achievement->description,
                'progress' => $userAchievement->progress,
                'target' => $userAchievement->achievement->target,
                'completed' => $userAchievement->completed,
                'reward_experience' => $userAchievement->achievement->reward_experience
            ];
        }

        return $achievementList;
    }

    private function getUserStatistics(int $userId): array
    {
        $user = User::find($userId);
        
        return [
            'total_creations' => $user->resources()->count(),
            'total_likes' => $user->total_likes ?? 0,
            'total_followers' => $user->follower_count ?? 0,
            'creation_streak' => 7, // 简化实现
            'most_popular_type' => 'story'
        ];
    }

    private function addExperience(int $userId, int $experience): void
    {
        $user = User::find($userId);
        $newExperience = ($user->experience ?? 0) + $experience;
        $newLevel = $this->calculateLevelFromExperience($newExperience);
        
        $user->update([
            'experience' => $newExperience,
            'level' => $newLevel
        ]);
    }

    private function getExperienceForLevel(int $level): int
    {
        // 简化的等级经验计算
        return $level * 1000;
    }

    private function calculateLevelFromExperience(int $experience): int
    {
        // 简化的等级计算
        return intval($experience / 1000) + 1;
    }

    private function getLevelTitle(int $level): string
    {
        $titles = [
            1 => '新手',
            5 => '初学者',
            10 => '熟练者',
            15 => '创作大师',
            20 => '传奇创作者'
        ];

        foreach (array_reverse($titles, true) as $levelThreshold => $title) {
            if ($level >= $levelThreshold) {
                return $title;
            }
        }

        return '新手';
    }

    private function getUserRankingValue($user, string $type)
    {
        switch ($type) {
            case 'level':
                return $user->level ?? 1;
            case 'experience':
                return $user->experience ?? 0;
            case 'creations':
                return $user->resources_count ?? 0;
            case 'likes':
                return $user->total_likes ?? 0;
            case 'followers':
                return $user->follower_count ?? 0;
            default:
                return 0;
        }
    }

    private function getUserTopBadge(int $userId): string
    {
        // 简化实现
        return '创作者';
    }

    private function getUserRank(int $userId, string $type): int
    {
        // 简化实现
        return 15;
    }

    private function getUserTaskStreak(int $userId): int
    {
        // 简化实现
        return 3;
    }

    private function calculateGrowthStatistics(int $userId): array
    {
        return [
            'total_level_ups' => GrowthHistory::where('user_id', $userId)->where('type', 'level_up')->count(),
            'total_achievements' => GrowthHistory::where('user_id', $userId)->where('type', 'achievement')->count(),
            'total_badges' => GrowthHistory::where('user_id', $userId)->where('type', 'badge')->count(),
            'total_tasks_completed' => UserDailyTask::where('user_id', $userId)->where('completed', true)->count()
        ];
    }

    private function getStartDateByPeriod(string $period): \Carbon\Carbon
    {
        switch ($period) {
            case 'week':
                return Carbon::now()->subWeek();
            case 'month':
                return Carbon::now()->subMonth();
            case 'year':
                return Carbon::now()->subYear();
            default:
                return Carbon::now()->subMonth();
        }
    }

    private function getGrowthTrends(int $userId, \Carbon\Carbon $startDate): array
    {
        // 简化实现
        return [
            ['date' => '2024-01-01', 'experience' => 50, 'level' => 14],
            ['date' => '2024-01-02', 'experience' => 75, 'level' => 14]
        ];
    }

    private function getActivityHeatmap(int $userId, \Carbon\Carbon $startDate): array
    {
        // 简化实现
        return [
            ['date' => '2024-01-01', 'activity_score' => 8],
            ['date' => '2024-01-02', 'activity_score' => 6]
        ];
    }

    private function getCurrentValueForGoal(int $userId, string $type): int
    {
        $user = User::find($userId);
        
        switch ($type) {
            case 'level':
                return $user->level ?? 1;
            case 'experience':
                return $user->experience ?? 0;
            case 'creations':
                return $user->resources()->count();
            case 'achievements':
                return UserAchievement::where('user_id', $userId)->where('completed', true)->count();
            default:
                return 0;
        }
    }

    /**
     * 获取用户成长里程碑
     * 修复500错误 - 添加缺失的getUserMilestones方法
     */
    public function getUserMilestones(int $userId): array
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return [
                    'milestones' => [],
                    'total_milestones' => 0,
                    'completed_milestones' => 0
                ];
            }

            // 模拟里程碑数据
            $milestones = [
                [
                    'id' => 1,
                    'title' => '创作新手',
                    'description' => '完成第一个创作项目',
                    'type' => 'creation',
                    'target' => 1,
                    'current' => min(1, $user->projects()->count()),
                    'completed' => $user->projects()->count() >= 1,
                    'reward_experience' => 100,
                    'icon' => 'milestone_creation_1.png'
                ],
                [
                    'id' => 2,
                    'title' => '积极创作者',
                    'description' => '完成10个创作项目',
                    'type' => 'creation',
                    'target' => 10,
                    'current' => min(10, $user->projects()->count()),
                    'completed' => $user->projects()->count() >= 10,
                    'reward_experience' => 500,
                    'icon' => 'milestone_creation_10.png'
                ],
                [
                    'id' => 3,
                    'title' => '等级达人',
                    'description' => '达到10级',
                    'type' => 'level',
                    'target' => 10,
                    'current' => min(10, $user->level ?? 1),
                    'completed' => ($user->level ?? 1) >= 10,
                    'reward_experience' => 300,
                    'icon' => 'milestone_level_10.png'
                ],
                [
                    'id' => 4,
                    'title' => '经验大师',
                    'description' => '获得5000经验值',
                    'type' => 'experience',
                    'target' => 5000,
                    'current' => min(5000, $user->experience ?? 0),
                    'completed' => ($user->experience ?? 0) >= 5000,
                    'reward_experience' => 200,
                    'icon' => 'milestone_exp_5000.png'
                ]
            ];

            // 计算进度
            foreach ($milestones as &$milestone) {
                $milestone['progress'] = $milestone['target'] > 0
                    ? round(($milestone['current'] / $milestone['target']) * 100, 1)
                    : 0;
                $milestone['progress'] = min(100, $milestone['progress']);
            }

            $totalMilestones = count($milestones);
            $completedMilestones = count(array_filter($milestones, function($m) {
                return $m['completed'];
            }));

            return [
                'milestones' => $milestones,
                'total_milestones' => $totalMilestones,
                'completed_milestones' => $completedMilestones,
                'completion_rate' => $totalMilestones > 0
                    ? round(($completedMilestones / $totalMilestones) * 100, 1)
                    : 0
            ];

        } catch (\Exception $e) {
            Log::error('获取用户成长里程碑失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'milestones' => [],
                'total_milestones' => 0,
                'completed_milestones' => 0,
                'completion_rate' => 0
            ];
        }
    }
}
