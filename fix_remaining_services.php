<?php

/**
 * 改进版自动修复脚本 - 处理剩余的复杂方法
 */

class ImprovedServiceDataFixer
{
    private $servicesPath;
    private $results = [];

    public function __construct($servicesPath = 'php/api/app/Services/Api')
    {
        $this->servicesPath = $servicesPath;
    }

    /**
     * 执行修复
     */
    public function fix()
    {
        echo "开始改进版自动修复...\n\n";
        
        // 重点修复的文件列表（基于检查结果）
        $priorityFiles = [
            'UserGrowthService.php' => 9,
            'SocialService.php' => 8,
            'RecommendationService.php' => 7,
            'WebSocketEventService.php' => 7,
            'WebSocketService.php' => 7,
            'ModelManagementService.php' => 6,
            'PublicationService.php' => 6,
            'ReviewService.php' => 6,
            'TemplateService.php' => 6,
            'PointsTransactionService.php' => 5,
            'ProjectManagementService.php' => 5,
            'TaskManagementService.php' => 5,
            'VersionControlService.php' => 5
        ];

        foreach ($priorityFiles as $fileName => $missingCount) {
            echo "修复高优先级文件: {$fileName} (缺失 {$missingCount} 个方法)\n";
            $this->fixSpecificFile($fileName);
        }

        $this->generateReport();
    }

    /**
     * 修复特定文件
     */
    private function fixSpecificFile($fileName)
    {
        $filePath = $this->servicesPath . '/' . $fileName;
        
        if (!file_exists($filePath)) {
            echo "  ❌ 文件不存在: {$fileName}\n";
            return;
        }

        $content = file_get_contents($filePath);
        $originalContent = $content;
        
        // 获取所有public方法
        $publicMethods = $this->extractPublicMethods($content);
        $fixedMethods = 0;

        foreach ($publicMethods as $method) {
            if ($method === '__construct') continue;
            
            if (!$this->hasServicesDataString($content, $method)) {
                $newContent = $this->fixMethodAdvanced($content, $method);
                if ($newContent !== $content) {
                    $content = $newContent;
                    $fixedMethods++;
                    echo "    ✅ 修复方法: {$method}()\n";
                }
            }
        }

        if ($fixedMethods > 0) {
            file_put_contents($filePath, $content);
            echo "  ✅ 总共修复了 {$fixedMethods} 个方法\n";
        } else {
            echo "  ℹ️ 无需修复或无法自动修复\n";
        }

        $this->results[$fileName] = $fixedMethods;
        echo "\n";
    }

    /**
     * 高级方法修复
     */
    private function fixMethodAdvanced($content, $methodName)
    {
        // 方法1: 查找没有try-catch的方法，添加完整的try-catch
        $pattern1 = '/public\s+function\s+' . preg_quote($methodName, '/') . '\s*\([^)]*\)\s*\{(?:(?!public\s+function|}\s*$).)*?\}/s';
        
        if (preg_match($pattern1, $content, $matches)) {
            $methodContent = $matches[0];
            
            // 检查是否已经有try-catch
            if (strpos($methodContent, 'try {') === false && strpos($methodContent, 'catch') === false) {
                // 没有try-catch，需要添加
                $newMethodContent = $this->wrapMethodWithTryCatch($methodContent, $methodName);
                return str_replace($methodContent, $newMethodContent, $content);
            }
            
            // 有try-catch但没有标准化错误处理
            if (strpos($methodContent, 'catch') !== false && strpos($methodContent, "'services_data' => \$services_data") === false) {
                $newMethodContent = $this->fixExistingCatchBlock($methodContent, $methodName);
                return str_replace($methodContent, $newMethodContent, $content);
            }
        }

        return $content;
    }

    /**
     * 为方法添加try-catch包装
     */
    private function wrapMethodWithTryCatch($methodContent, $methodName)
    {
        // 提取方法签名和方法体
        preg_match('/^(public\s+function\s+' . preg_quote($methodName, '/') . '\s*\([^)]*\)\s*)\{(.+)\}$/s', $methodContent, $matches);
        
        if (count($matches) < 3) {
            return $methodContent; // 无法解析，返回原内容
        }

        $signature = $matches[1];
        $body = trim($matches[2]);

        // 生成新的方法内容
        $newMethodContent = $signature . "{\n";
        $newMethodContent .= "        try {\n";
        
        // 缩进原有的方法体
        $indentedBody = $this->indentCode($body, 12);
        $newMethodContent .= $indentedBody . "\n";
        
        $newMethodContent .= "        } catch (\\Exception \$e) {\n";
        $newMethodContent .= "            \$services_data = [\n";
        $newMethodContent .= "                // 关键的非敏感字段\n";
        $newMethodContent .= "            ];\n\n";
        $newMethodContent .= "            Log::error('" . $this->generateErrorMessage($methodName) . "', [\n";
        $newMethodContent .= "                'method' => __METHOD__,\n";
        $newMethodContent .= "                'services_data' => \$services_data,\n";
        $newMethodContent .= "                'error' => \$e->getMessage(),\n";
        $newMethodContent .= "                'trace' => \$e->getTraceAsString()\n";
        $newMethodContent .= "            ]);\n\n";
        $newMethodContent .= "            return [\n";
        $newMethodContent .= "                'code' => ApiCodeEnum::MY_SERVICE_ERROR,\n";
        $newMethodContent .= "                'message' => '" . $this->generateErrorMessage($methodName) . "',\n";
        $newMethodContent .= "                'data' => null\n";
        $newMethodContent .= "            ];\n";
        $newMethodContent .= "        }\n";
        $newMethodContent .= "    }";

        return $newMethodContent;
    }

    /**
     * 修复现有的catch块
     */
    private function fixExistingCatchBlock($methodContent, $methodName)
    {
        // 查找catch块并替换
        $pattern = '/} catch \([^)]+\) \{[^}]*\}/s';
        
        $newCatchBlock = "} catch (\\Exception \$e) {\n";
        $newCatchBlock .= "            \$services_data = [\n";
        $newCatchBlock .= "                // 关键的非敏感字段\n";
        $newCatchBlock .= "            ];\n\n";
        $newCatchBlock .= "            Log::error('" . $this->generateErrorMessage($methodName) . "', [\n";
        $newCatchBlock .= "                'method' => __METHOD__,\n";
        $newCatchBlock .= "                'services_data' => \$services_data,\n";
        $newCatchBlock .= "                'error' => \$e->getMessage(),\n";
        $newCatchBlock .= "                'trace' => \$e->getTraceAsString()\n";
        $newCatchBlock .= "            ]);\n\n";
        $newCatchBlock .= "            return [\n";
        $newCatchBlock .= "                'code' => ApiCodeEnum::MY_SERVICE_ERROR,\n";
        $newCatchBlock .= "                'message' => '" . $this->generateErrorMessage($methodName) . "',\n";
        $newCatchBlock .= "                'data' => null\n";
        $newCatchBlock .= "            ];\n";
        $newCatchBlock .= "        }";

        return preg_replace($pattern, $newCatchBlock, $methodContent);
    }

    /**
     * 代码缩进
     */
    private function indentCode($code, $spaces)
    {
        $lines = explode("\n", $code);
        $indentedLines = [];
        
        foreach ($lines as $line) {
            if (trim($line) !== '') {
                $indentedLines[] = str_repeat(' ', $spaces) . $line;
            } else {
                $indentedLines[] = '';
            }
        }
        
        return implode("\n", $indentedLines);
    }

    /**
     * 生成错误消息
     */
    private function generateErrorMessage($methodName)
    {
        $messages = [
            'get' => '获取数据失败',
            'create' => '创建失败',
            'update' => '更新失败',
            'delete' => '删除失败',
            'manage' => '管理操作失败',
            'execute' => '执行失败',
            'process' => '处理失败',
            'generate' => '生成失败',
            'validate' => '验证失败',
            'check' => '检查失败',
            'send' => '发送失败',
            'push' => '推送失败'
        ];

        foreach ($messages as $prefix => $message) {
            if (strpos(strtolower($methodName), $prefix) === 0) {
                return $message;
            }
        }

        return '操作失败';
    }

    /**
     * 提取所有 public 方法
     */
    private function extractPublicMethods($content)
    {
        $methods = [];
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $content, $matches);
        
        if (!empty($matches[1])) {
            $methods = $matches[1];
        }
        
        return array_unique($methods);
    }

    /**
     * 检查方法是否包含 'services_data' => $services_data 字符串
     */
    private function hasServicesDataString($content, $methodName)
    {
        $pattern = '/public\s+function\s+' . preg_quote($methodName, '/') . '\s*\(/';
        preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
        
        if (empty($matches)) {
            return false;
        }
        
        $methodStart = $matches[0][1];
        $nextMethodPattern = '/public\s+function\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(/';
        preg_match($nextMethodPattern, $content, $nextMatches, PREG_OFFSET_CAPTURE, $methodStart + 1);
        
        $methodEnd = !empty($nextMatches) ? $nextMatches[0][1] : strlen($content);
        $methodContent = substr($content, $methodStart, $methodEnd - $methodStart);
        
        return strpos($methodContent, "'services_data' => \$services_data") !== false;
    }

    /**
     * 生成修复报告
     */
    private function generateReport()
    {
        echo "==================== 改进版修复报告 ====================\n\n";
        
        $totalFixedMethods = array_sum($this->results);
        $fixedFiles = count(array_filter($this->results));

        echo "📊 修复统计：\n";
        echo "- 处理文件数：" . count($this->results) . "\n";
        echo "- 修复文件数：{$fixedFiles}\n";
        echo "- 总修复方法数：{$totalFixedMethods}\n\n";

        if ($fixedFiles > 0) {
            echo "🔧 修复详情：\n";
            foreach ($this->results as $fileName => $fixedCount) {
                if ($fixedCount > 0) {
                    echo "- {$fileName}: 修复了 {$fixedCount} 个方法\n";
                }
            }
            echo "\n";
        }

        echo "✅ 改进版修复完成！\n";
    }
}

// 执行修复
try {
    $fixer = new ImprovedServiceDataFixer();
    $fixer->fix();
} catch (Exception $e) {
    echo "❌ 修复过程中发生错误: " . $e->getMessage() . "\n";
}
